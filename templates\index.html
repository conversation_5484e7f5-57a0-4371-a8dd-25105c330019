<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My YouTube Dashboard</title>

    <!-- Auto-refresh when videos are being scraped in background -->
    {% if has_pending_videos %}
    <meta http-equiv="refresh" content="3">
    {% endif %}

    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/nprogress.min.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet">
</head>
<body>
    <!-- Universal Command Center - Single Unified Toolbar -->
    <header class="command-center">
        <div class="command-center-content">
            <!-- Simplified Centered Layout -->
            <div class="toolbar-main">
                <form method="GET" class="main-search-form">
                    <!-- Preserve current state -->
                    <input type="hidden" name="sort_by" value="{{ sort_by }}">
                    <input type="hidden" name="group_filter" value="{{ group_filter }}">
                    <input type="hidden" name="channel_id" value="{{ channel_filter }}">
                    <input type="hidden" name="grouping_filter" value="{{ grouping_filter }}">
                    {% if min_views %}<input type="hidden" name="min_views" value="{{ min_views }}">{% endif %}
                    {% if max_views %}<input type="hidden" name="max_views" value="{{ max_views }}">{% endif %}
                    {% if date_from %}<input type="hidden" name="date_from" value="{{ date_from }}">{% endif %}
                    {% if date_to %}<input type="hidden" name="date_to" value="{{ date_to }}">{% endif %}

                    <div class="search-input-container">
                        <span class="material-symbols-outlined search-icon-left">search</span>
                        <input type="search"
                               name="q"
                               value="{{ search_query or '' }}"
                               placeholder="Search videos by title, channel, or description..."
                               class="main-search-input">
                        {% if search_query %}
                        <a href="{{ url_for('index', sort_by=sort_by, group_filter=group_filter, channel_id=channel_filter, grouping_filter=grouping_filter) }}"
                           class="clear-search-button" title="Clear search">
                            <span class="material-symbols-outlined">close</span>
                        </a>
                        {% endif %}
                    </div>
                </form>

                <!-- Advanced Filters Toggle -->
                <button class="advanced-filters-button" onclick="toggleAdvancedFilters()">
                    <span class="material-symbols-outlined">tune</span>
                    Advanced
                    <span class="material-symbols-outlined toggle-arrow">expand_more</span>
                </button>
            </div>

            <!-- Toolbar Actions -->
                <!-- View Controls Group -->
                <div class="view-controls-group">
                    <!-- Sort Dropdown -->
                    <form method="GET" class="sort-form-inline">
                        <input type="hidden" name="group_filter" value="{{ group_filter }}">
                        <input type="hidden" name="grouping_filter" value="{{ grouping_filter }}">
                        <div class="sort-control">
                            <label for="sort_by_header" class="sort-label">
                                <span class="material-symbols-outlined">sort</span>
                            </label>
                            <select name="sort_by" id="sort_by_header" class="sort-dropdown-compact" onchange="this.form.submit()">
                                <option value="date_added_newest" {% if sort_by == 'date_added_newest' %}selected{% endif %}>Newest Added</option>
                                <option value="date_added_oldest" {% if sort_by == 'date_added_oldest' %}selected{% endif %}>Oldest Added</option>
                                <option value="popular" {% if sort_by == 'popular' %}selected{% endif %}>Most Popular</option>
                                <option value="date_published_newest" {% if sort_by == 'date_published_newest' %}selected{% endif %}>Newest Upload</option>
                                <option value="date_published_oldest" {% if sort_by == 'date_published_oldest' %}selected{% endif %}>Oldest Upload</option>
                                <option value="alphabetical" {% if sort_by == 'alphabetical' %}selected{% endif %}>A-Z</option>
                                <option value="channel" {% if sort_by == 'channel' %}selected{% endif %}>Channel</option>
                            </select>
                        </div>
                    </form>

                    <!-- Grouping Filter -->
                    <form method="GET" class="grouping-filter-form">
                        <input type="hidden" name="sort_by" value="{{ sort_by }}">
                        <input type="hidden" name="group_filter" value="{{ group_filter }}">
                        <div class="grouping-filter-group" role="group" aria-label="Filter videos by grouping status">
                            <button type="submit" name="grouping_filter" value="all"
                                    class="grouping-filter-btn {% if grouping_filter == 'all' %}active{% endif %}"
                                    title="Show all videos">
                                <span class="material-symbols-outlined">video_library</span>
                                All
                            </button>
                            <button type="submit" name="grouping_filter" value="grouped"
                                    class="grouping-filter-btn {% if grouping_filter == 'grouped' %}active{% endif %}"
                                    title="Show only videos that belong to groups">
                                <span class="material-symbols-outlined">folder_managed</span>
                                Grouped
                            </button>
                            <button type="submit" name="grouping_filter" value="ungrouped"
                                    class="grouping-filter-btn {% if grouping_filter == 'ungrouped' %}active{% endif %}"
                                    title="Show only videos that don't belong to any groups">
                                <span class="material-symbols-outlined">folder_open</span>
                                Ungrouped
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Global Actions Group -->
                <div class="global-actions-group">
                    <!-- Primary Actions -->
                    <button class="command-action-button primary-action" onclick="toggleAddVideoModal()">
                        <span class="material-symbols-outlined">add</span>
                        Add Videos
                    </button>

                    <!-- Secondary Actions -->
                    <div class="action-dropdown">
                        <button class="command-action-button secondary-action" onclick="toggleImportExportMenu()">
                            <span class="material-symbols-outlined">import_export</span>
                            <span class="material-symbols-outlined dropdown-arrow">expand_more</span>
                        </button>
                        <div class="dropdown-menu" id="importExportDropdown">
                            <!-- Export Options -->
                            <div class="dropdown-section">
                                <div class="dropdown-label">Export</div>
                                <a href="/export/csv?sort_by={{ sort_by }}&group_filter={{ group_filter }}" class="dropdown-item">
                                    <span class="material-symbols-outlined">table_view</span>
                                    Download as CSV
                                </a>
                                <a href="/export/json?sort_by={{ sort_by }}&group_filter={{ group_filter }}" class="dropdown-item">
                                    <span class="material-symbols-outlined">code</span>
                                    Download as JSON
                                </a>
                            </div>
                            <!-- Import Options -->
                            <div class="dropdown-section">
                                <div class="dropdown-label">Import</div>
                                <form action="/import_data" method="POST" enctype="multipart/form-data" class="import-form-inline">
                                    <input type="hidden" name="sort_by" value="{{ sort_by }}">
                                    <input type="hidden" name="group_filter" value="{{ group_filter }}">
                                    <label for="import_file_header" class="dropdown-item file-upload-item">
                                        <span class="material-symbols-outlined">upload_file</span>
                                        <span>Choose File (JSON/CSV)</span>
                                        <input type="file" id="import_file_header" name="import_file" accept=".json,.csv" class="file-input-hidden" onchange="handleFileUpload(this)">
                                    </label>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Utility Actions -->
                    <button class="command-action-button utility-action" onclick="refreshAllData()">
                        <span class="material-symbols-outlined">refresh</span>
                    </button>

                    <button class="command-action-button utility-action" onclick="checkDeadLinks()">
                        <span class="material-symbols-outlined">link_off</span>
                    </button>

                    <!-- Navigation -->
                    <a href="/channels" class="command-action-button nav-action">
                        <span class="material-symbols-outlined">account_circle</span>
                        Channels
                    </a>
                </div>
            </div>
        </div>
    </header>



    <!-- Advanced Filters Panel (Moved below Filter Bar) -->
    <div class="advanced-filters-panel" id="advancedFilters" style="display: none;">
        <form method="GET" class="advanced-filters-form">
            <!-- Preserve current state -->
            <input type="hidden" name="sort_by" value="{{ sort_by }}">
            <input type="hidden" name="group_filter" value="{{ group_filter }}">
            <input type="hidden" name="channel_id" value="{{ channel_filter }}">
            <input type="hidden" name="grouping_filter" value="{{ grouping_filter }}">
            {% if search_query %}<input type="hidden" name="q" value="{{ search_query }}">{% endif %}

            <div class="filter-grid">
                <!-- View Count Range -->
                <div class="filter-group">
                    <label class="filter-label">
                        <span class="material-symbols-outlined">visibility</span>
                        View Count Range
                    </label>
                    <div class="range-inputs">
                        <input type="number"
                               name="min_views"
                               value="{{ min_views or '' }}"
                               placeholder="Min views"
                               class="range-input">
                        <span class="range-separator">to</span>
                        <input type="number"
                               name="max_views"
                               value="{{ max_views or '' }}"
                               placeholder="Max views"
                               class="range-input">
                    </div>
                </div>

                <!-- Upload Date Range -->
                <div class="filter-group">
                    <label class="filter-label">
                        <span class="material-symbols-outlined">calendar_today</span>
                        Upload Date Range
                    </label>
                    <div class="range-inputs">
                        <input type="date"
                               name="date_from"
                               value="{{ date_from or '' }}"
                               class="date-input">
                        <span class="range-separator">to</span>
                        <input type="date"
                               name="date_to"
                               value="{{ date_to or '' }}"
                               class="date-input">
                    </div>
                </div>
            </div>

            <div class="filter-actions">
                <button type="submit" class="apply-filters-button">
                    <span class="material-symbols-outlined">filter_list</span>
                    Apply Filters
                </button>
                <a href="{{ url_for('index', sort_by=sort_by, group_filter=group_filter, channel_id=channel_filter, grouping_filter=grouping_filter, q=search_query if search_query else none) }}"
                   class="clear-filters-button">
                    <span class="material-symbols-outlined">clear_all</span>
                    Clear Filters
                </a>
            </div>
        </form>
    </div>

    {% if videos %}
        <!-- Content Wrapper with Unbreakable Grid Layout -->
        <main class="content-wrapper">
                <!-- Group Sidebar -->
                <aside class="group-sidebar">
                    <div class="sidebar-header">
                        <h3>
                            <span class="material-symbols-outlined">folder</span>
                            Groups
                        </h3>
                    </div>

                    <!-- Create Group Form -->
                    <form action="/create_group" method="POST" class="create-group-form">
                        <!-- Hidden inputs to preserve sorting state -->
                        <input type="hidden" name="sort_by" value="{{ sort_by }}">
                        <input type="hidden" name="group_filter" value="{{ group_filter }}">
                        <input type="hidden" name="grouping_filter" value="{{ grouping_filter }}">
                        <div class="input-group">
                            <input type="text"
                                   name="group_name"
                                   placeholder="New group name..."
                                   required
                                   class="group-input">
                            <button type="submit" class="create-group-button">
                                <span class="material-symbols-outlined">add</span>
                            </button>
                        </div>
                    </form>

                    <!-- Group List -->
                    <div class="group-list">
                        <!-- All Videos Option -->
                        <a href="/?group_filter=all&sort_by={{ sort_by }}&grouping_filter={{ grouping_filter }}"
                           class="group-item {% if group_filter == 'all' %}active{% endif %}">
                            <span class="material-symbols-outlined">video_library</span>
                            <span class="group-name">All Videos</span>
                            <span class="video-count">{{ total_video_count }}</span>
                        </a>

                        <!-- Individual Groups -->
                        {% for group in groups %}
                        <div class="group-item-container">
                            <a href="/?group_filter={{ group.id }}&sort_by={{ sort_by }}&grouping_filter={{ grouping_filter }}"
                               class="group-item {% if group_filter == group.id %}active{% endif %}">
                                <span class="material-symbols-outlined">folder</span>
                                <span class="group-name">{{ group.name }}</span>
                                <span class="video-count">{{ group.video_count }}</span>
                            </a>
                            <a href="/delete_group/{{ group.id }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}&grouping_filter={{ grouping_filter }}"
                               class="delete-group-button"
                               onclick="return confirm('Delete group \'{{ group.name }}\'? Videos will not be deleted.')">
                                <span class="material-symbols-outlined">delete</span>
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </aside>

                <!-- Video Content -->
                <div class="video-content">
                    <div class="video-grid">
                {% for video in videos %}
                    {% if video.status == 'pending' %}
                        <!-- PENDING VIDEO CARD (Being Scraped) -->
                        <div class="video-card video-card-pending" data-status="pending" data-video-id="{{ video.id }}">
                            <div class="thumbnail-container">
                                <div class="thumbnail-placeholder">
                                    <img src="{{ video.thumbnail }}"
                                         alt="Thumbnail for {{ video.id }}"
                                         class="thumbnail thumbnail-loading"
                                         loading="lazy">
                                    <div class="loading-overlay">
                                        <div class="spinner"></div>
                                    </div>
                                </div>
                                <!-- Loading length badge -->
                                <div class="video-length-badge loading-badge">{{ video.length }}</div>

                                <div class="video-actions">
                                    <a href="/remove_video/{{ video.id }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}"
                                       class="remove-button cancel-button"
                                       onclick="return confirm('Cancel scraping this video?')"
                                       title="Cancel scraping">
                                        <span class="material-symbols-outlined">cancel</span>
                                        <span class="button-text">Cancel</span>
                                    </a>
                                </div>
                            </div>

                            <div class="video-info">
                                <div class="channel-icon-placeholder">
                                    <div class="spinner-small"></div>
                                </div>

                                <div class="video-text">
                                    <h3 class="video-title loading-title">
                                        <span class="loading-text">{{ video.title }}</span>
                                        <div class="loading-dots">
                                            <span>.</span><span>.</span><span>.</span>
                                        </div>
                                    </h3>
                                    <p class="channel-name loading-channel">{{ video.channelName }}</p>
                                    <p class="video-meta loading-meta">{{ video.views }} • {{ video.uploadDate }}</p>
                                </div>
                            </div>
                        </div>
                    {% elif video.status == 'error' %}
                        <!-- ERROR VIDEO CARD (Failed to Scrape) -->
                        <div class="video-card video-card-error" data-status="error" data-video-id="{{ video.id }}">
                            <div class="thumbnail-container">
                                <div class="thumbnail-error">
                                    <img src="{{ video.thumbnail }}"
                                         alt="Thumbnail for {{ video.id }}"
                                         class="thumbnail"
                                         loading="lazy">
                                    <div class="error-overlay">
                                        <span class="material-symbols-outlined error-icon">error</span>
                                    </div>
                                </div>
                                <div class="video-length-badge error-badge">Error</div>

                                <div class="video-actions">
                                    <a href="/remove_video/{{ video.id }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}"
                                       class="remove-button"
                                       onclick="return confirm('Remove this failed video from your feed?')"
                                       title="Remove video">
                                        <span class="material-symbols-outlined">close</span>
                                    </a>
                                </div>
                            </div>

                            <div class="video-info">
                                <div class="channel-icon error-icon-placeholder">
                                    <span class="material-symbols-outlined">error</span>
                                </div>

                                <div class="video-text">
                                    <h3 class="video-title error-title">{{ video.title }}</h3>
                                    <p class="channel-name error-channel">Failed to load channel</p>
                                    <p class="video-meta error-meta">{{ video.error_message or 'Scraping failed' }}</p>
                                </div>
                            </div>
                        </div>
                    {% elif video.status == 'deleted' %}
                        <!-- DELETED VIDEO CARD (Dead Link) -->
                        <div class="video-card video-card-deleted" data-status="deleted" data-video-id="{{ video.id }}">
                            <div class="thumbnail-container">
                                <div class="thumbnail-deleted">
                                    <img src="{{ video.thumbnail }}"
                                         alt="Thumbnail for {{ video.id }}"
                                         class="thumbnail thumbnail-grayed">
                                    <div class="status-overlay">
                                        <span class="material-symbols-outlined status-icon">link_off</span>
                                        <span class="status-text">Video Deleted</span>
                                    </div>
                                </div>
                            </div>
                            <div class="video-info">
                                <h3 class="video-title deleted-title">{{ video.title }}</h3>
                                <p class="video-channel">
                                    <span class="channel-name">{{ video.channelName }}</span>
                                </p>
                                <p class="video-meta deleted-meta">This video has been removed from YouTube</p>

                                <div class="video-actions">
                                    <a href="/remove_video/{{ video.id }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}"
                                       class="remove-button"
                                       onclick="return confirm('Remove this deleted video from your feed?')"
                                       title="Remove video">
                                        <span class="material-symbols-outlined">delete</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% elif video.status == 'private' %}
                        <!-- PRIVATE VIDEO CARD (Private/Unlisted) -->
                        <div class="video-card video-card-private" data-status="private" data-video-id="{{ video.id }}">
                            <div class="thumbnail-container">
                                <div class="thumbnail-private">
                                    <img src="{{ video.thumbnail }}"
                                         alt="Thumbnail for {{ video.id }}"
                                         class="thumbnail thumbnail-grayed">
                                    <div class="status-overlay">
                                        <span class="material-symbols-outlined status-icon">lock</span>
                                        <span class="status-text">Private Video</span>
                                    </div>
                                </div>
                            </div>
                            <div class="video-info">
                                <h3 class="video-title private-title">{{ video.title }}</h3>
                                <p class="video-channel">
                                    <span class="channel-name">{{ video.channelName }}</span>
                                </p>
                                <p class="video-meta private-meta">This video is private or unlisted</p>

                                <div class="video-actions">
                                    <a href="/remove_video/{{ video.id }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}"
                                       class="remove-button"
                                       onclick="return confirm('Remove this private video from your feed?')"
                                       title="Remove video">
                                        <span class="material-symbols-outlined">visibility_off</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- NORMAL VIDEO CARD (Completed) -->
                        <div class="video-card" data-status="complete" data-video-id="{{ video.id }}">
                            <div class="thumbnail-container">
                                <a href="{{ video.url }}" target="_blank" rel="noopener noreferrer">
                                    <img src="{{ video.thumbnail }}"
                                         alt="Thumbnail for {{ video.title }}"
                                         class="thumbnail"
                                         loading="lazy">
                                </a>
                                <!-- Video length badge -->
                                <div class="video-length-badge">{{ video.length }}</div>

                                <div class="video-actions">
                                    <button class="group-manage-button"
                                            onclick="openGroupModal('{{ video.id }}')"
                                            title="Manage groups">
                                        <span class="material-symbols-outlined">folder_open</span>
                                    </button>
                                    <a href="/remove_video/{{ video.url.split('v=')[1] }}?sort_by={{ sort_by }}&group_filter={{ group_filter }}&grouping_filter={{ grouping_filter }}"
                                       class="remove-button"
                                       onclick="return confirm('Remove this video from your feed?')"
                                       title="Remove video">
                                        <span class="material-symbols-outlined">close</span>
                                    </a>
                                </div>
                            </div>

                            <div class="video-info">
                                <img src="{{ video.channelIconUrl }}"
                                     alt="{{ video.channelName }}"
                                     class="channel-icon"
                                     loading="lazy"
                                     onerror="this.src='https://www.youtube.com/img/desktop/yt_1200.png'">

                                <div class="video-text">
                                    <h3 class="video-title">
                                        <a href="{{ video.url }}" target="_blank" rel="noopener noreferrer">
                                            {{ video.title }}
                                        </a>
                                    </h3>
                                    <p class="channel-name">
                                        <a href="{{ video.channelUrl }}" target="_blank" rel="noopener noreferrer" class="channel-link">
                                            {{ video.channelName }}
                                        </a>
                                    </p>

                                    <!-- Group Pills -->
                                    {% if video.groupDetails %}
                                    <div class="group-pills">
                                        {% for group in video.groupDetails %}
                                        <a href="/?group_filter={{ group.id }}&sort_by={{ sort_by }}&grouping_filter={{ grouping_filter }}" class="group-link">
                                            <span class="group-pill">{{ group.name }}</span>
                                        </a>
                                        {% endfor %}
                                    </div>
                                    {% endif %}

                                    <p class="video-meta">{{ video.views }} • {{ video.uploadDate }}{% if video.timeAgo %} ({{ video.timeAgo }}){% endif %}</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
                    </div>
                </div>

                <!-- Pagination Controls -->
                {% set endpoint = 'index' %}
                {% include 'pagination_controls.html' %}
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-content">
                    <span class="material-symbols-outlined empty-icon">video_library</span>
                    <h2>No videos yet</h2>
                    <p>Add your first YouTube video using the form above to get started!</p>
                    <div class="example-urls">
                        <p><strong>Supported URL formats:</strong></p>
                        <ul>
                            <li>https://www.youtube.com/watch?v=VIDEO_ID</li>
                            <li>https://youtu.be/VIDEO_ID</li>
                            <li>https://www.youtube.com/embed/VIDEO_ID</li>
                            <li>https://www.youtube.com/shorts/VIDEO_ID</li>
                        </ul>
                    </div>
                </div>
            </div>
        {% endif %}
    </main>

    <!-- Add Video Modal -->
    <div id="addVideoModal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>Add YouTube Videos</h3>
                <button class="modal-close" onclick="closeAddVideoModal()">
                    <span class="material-symbols-outlined">close</span>
                </button>
            </div>
            <form action="/add_video" method="POST" class="add-video-modal-form">
                <!-- Hidden inputs to preserve sorting state -->
                <input type="hidden" name="sort_by" value="{{ sort_by }}">
                <input type="hidden" name="group_filter" value="{{ group_filter }}">
                <input type="hidden" name="grouping_filter" value="{{ grouping_filter }}">
                <div class="modal-body">
                    <div class="add-video-instructions">
                        <p><strong>Supported URL formats:</strong></p>
                        <ul>
                            <li>Individual videos: <code>youtube.com/watch?v=...</code></li>
                            <li>Playlists: <code>youtube.com/playlist?list=...</code> (imports all videos)</li>
                            <li>Short URLs: <code>youtu.be/...</code></li>
                            <li>Embedded URLs: <code>youtube.com/embed/...</code></li>
                            <li>Shorts: <code>youtube.com/shorts/...</code></li>
                        </ul>
                    </div>
                    <textarea name="video_url"
                              placeholder="Paste YouTube video links or playlist URLs here, each on a new line...&#10;&#10;Examples:&#10;https://www.youtube.com/watch?v=dQw4w9WgXcQ&#10;https://youtu.be/dQw4w9WgXcQ&#10;https://www.youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMt9H1mu_0Qk0XnPXX"
                              required
                              class="video-input-modal"
                              rows="8"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-button" onclick="closeAddVideoModal()">Cancel</button>
                    <button type="submit" class="save-button primary-button">
                        <span class="material-symbols-outlined">add</span>
                        Add Videos
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Group Management Modal -->
    <div id="groupModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Manage Groups</h3>
                <button class="modal-close" onclick="closeGroupModal()">
                    <span class="material-symbols-outlined">close</span>
                </button>
            </div>
            <form id="groupForm" action="/update_video_groups" method="POST">
                <input type="hidden" id="modalVideoId" name="video_id" value="">
                <!-- Hidden inputs to preserve sorting state -->
                <input type="hidden" name="sort_by" value="{{ sort_by }}">
                <input type="hidden" name="group_filter" value="{{ group_filter }}">
                <input type="hidden" name="grouping_filter" value="{{ grouping_filter }}">
                <div class="modal-body">
                    <div class="group-checkboxes">
                        {% for group in groups %}
                        <label class="checkbox-item">
                            <input type="checkbox" name="groups" value="{{ group.id }}" class="group-checkbox">
                            <span class="checkbox-label">{{ group.name }}</span>
                        </label>
                        {% endfor %}
                        {% if not groups %}
                        <p class="no-groups">No groups created yet. Create a group using the sidebar.</p>
                        {% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="cancel-button" onclick="closeGroupModal()">Cancel</button>
                    <button type="submit" class="save-button">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Global Loading Overlay -->
    <div id="globalLoadingOverlay" class="global-loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Processing...</div>
        </div>
    </div>

    <!-- Toast Notification Container -->
    <div id="toastContainer" class="toast-container"></div>

    <footer class="footer">
        <p>&copy; 2024 My YouTube Dashboard - Built without API keys using web scraping</p>
    </footer>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            const modalForm = document.querySelector('.add-video-modal-form');
            const modalInput = document.querySelector('.video-input-modal');
            const modalButton = modalForm ? modalForm.querySelector('.primary-button') : null;

            if (modalForm && modalInput && modalButton) {
                modalForm.addEventListener('submit', function(e) {
                    const url = modalInput.value.trim();
                    if (!url) {
                        e.preventDefault();
                        alert('Please enter a YouTube URL');
                        return;
                    }

                    // Basic URL validation
                    const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com|youtu\.be)/;
                    if (!youtubeRegex.test(url)) {
                        e.preventDefault();
                        alert('Please enter a valid YouTube URL');
                        return;
                    }

                    // Show loading state
                    modalButton.innerHTML = '<span class="material-symbols-outlined">hourglass_empty</span> Adding...';
                    modalButton.disabled = true;
                });
            }

            // Enhanced search functionality
            const searchInput = document.querySelector('.main-search-input');
            if (searchInput) {
                // Auto-submit search on Enter
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.closest('form').submit();
                    }
                });

                // Add search-as-you-type with debounce (optional enhancement)
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    // Uncomment below for live search (currently disabled for performance)
                    // searchTimeout = setTimeout(() => {
                    //     this.closest('form').submit();
                    // }, 500);
                });
            }

            // Add keyboard shortcut (Ctrl+K or Cmd+K) to focus search or open add video modal
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    } else {
                        toggleAddVideoModal();
                    }
                }
            });
        });

        // Video data for JavaScript access
        const videoData = {{ videos|tojson }};

        // Add Video Modal functionality
        function toggleAddVideoModal() {
            const modal = document.getElementById('addVideoModal');
            modal.style.display = 'block';
            // Focus on textarea when modal opens
            setTimeout(() => {
                const textarea = modal.querySelector('.video-input-modal');
                if (textarea) textarea.focus();
            }, 100);
        }

        function closeAddVideoModal() {
            const modal = document.getElementById('addVideoModal');
            modal.style.display = 'none';
        }

        // Import/Export dropdown functionality
        function toggleImportExportMenu() {
            const dropdown = document.getElementById('importExportDropdown');
            dropdown.classList.toggle('show');
        }

        function handleFileUpload(input) {
            if (input.files.length > 0) {
                // Auto-submit the form when file is selected
                input.closest('form').submit();
            }
        }

        // Global action functions with loading states
        function refreshAllData() {
            if (confirm('This will update all video data (view counts, etc.) and may take a while. Continue?')) {
                // Show loading state
                const button = event.target.closest('.utility-action');
                if (button) {
                    const originalContent = button.innerHTML;
                    button.innerHTML = '<span class="material-symbols-outlined">hourglass_empty</span> Refreshing...';
                    button.disabled = true;
                    button.classList.add('loading-state');
                }
                window.location.href = `/refresh_all?sort_by={{ sort_by }}&group_filter={{ group_filter }}`;
            }
        }

        function checkDeadLinks() {
            if (confirm('This will check all videos for dead links and may take a while. Continue?')) {
                // Show loading state
                const button = event.target.closest('.utility-action');
                if (button) {
                    const originalContent = button.innerHTML;
                    button.innerHTML = '<span class="material-symbols-outlined">link_off</span> Checking...';
                    button.disabled = true;
                    button.classList.add('loading-state');
                }
                window.location.href = `/audit_videos?sort_by={{ sort_by }}&group_filter={{ group_filter }}`;
            }
        }

        // Enhanced form submission feedback
        function showFormSubmissionFeedback(form, button) {
            if (button) {
                const originalContent = button.innerHTML;
                button.innerHTML = '<span class="material-symbols-outlined">hourglass_empty</span> Processing...';
                button.disabled = true;
                button.classList.add('loading-state');
            }
        }

        // Toast Notification System
        function showToast(title, message, type = 'info', duration = 5000) {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            const iconMap = {
                success: 'check_circle',
                error: 'error',
                warning: 'warning',
                info: 'info'
            };

            toast.innerHTML = `
                <span class="material-symbols-outlined toast-icon">${iconMap[type]}</span>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="closeToast(this)">
                    <span class="material-symbols-outlined">close</span>
                </button>
            `;

            container.appendChild(toast);

            // Auto-remove after duration
            setTimeout(() => {
                closeToast(toast.querySelector('.toast-close'));
            }, duration);
        }

        function closeToast(button) {
            const toast = button.closest('.toast');
            toast.classList.add('fade-out');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }

        // Show welcome toast on page load (optional)
        document.addEventListener('DOMContentLoaded', function() {
            // Uncomment to show welcome message
            // showToast('Welcome!', 'Your YouTube dashboard has been redesigned with a modern interface.', 'success', 3000);
        });

        // Close dropdowns when clicking outside
        window.onclick = function(event) {
            // Close import/export dropdown
            if (!event.target.matches('.global-action-button') && !event.target.closest('.action-dropdown')) {
                const importExportDropdown = document.getElementById('importExportDropdown');
                if (importExportDropdown && importExportDropdown.classList.contains('show')) {
                    importExportDropdown.classList.remove('show');
                }
            }

            // Close modals when clicking outside
            const addVideoModal = document.getElementById('addVideoModal');
            const groupModal = document.getElementById('groupModal');
            if (event.target === addVideoModal) {
                closeAddVideoModal();
            }
            if (event.target === groupModal) {
                closeGroupModal();
            }
        }

        // Group modal functionality with scroll preservation
        function openGroupModal(videoId) {
            // PRESERVE SCROLL POSITION before opening modal
            if (window.preserveScrollPosition) {
                window.preserveScrollPosition();
            }

            const modal = document.getElementById('groupModal');
            const videoIdInput = document.getElementById('modalVideoId');
            const checkboxes = document.querySelectorAll('.group-checkbox');

            // Set video ID
            videoIdInput.value = videoId;

            // Find video data and check appropriate boxes
            const video = videoData.find(v => v.id === videoId);
            if (video) {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = video.groups.includes(checkbox.value);
                });
            }

            modal.style.display = 'block';
        }

        function closeGroupModal() {
            const modal = document.getElementById('groupModal');
            modal.style.display = 'none';
        }

        // Add scroll preservation to group form submission
        const groupForm = document.getElementById('groupForm');
        if (groupForm) {
            groupForm.addEventListener('submit', function() {
                // Preserve scroll position before form submission
                if (window.preserveScrollPosition) {
                    window.preserveScrollPosition();
                }
            });
        }

        // Advanced Filters Toggle
        function toggleAdvancedFilters() {
            const panel = document.getElementById('advancedFilters');
            const button = document.querySelector('.advanced-filters-button');
            const arrow = document.querySelector('.toggle-arrow');

            if (panel.style.display === 'none' || panel.style.display === '') {
                panel.style.display = 'block';
                arrow.classList.add('rotated');
                button.classList.add('active');
            } else {
                panel.style.display = 'none';
                arrow.classList.remove('rotated');
                button.classList.remove('active');
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('groupModal');
            if (event.target === modal) {
                closeGroupModal();
            }
        }
    </script>

    <!-- NProgress - Global Progress Bar -->
    <script src="{{ url_for('static', filename='vendor/nprogress.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/progress.js') }}"></script>
    <script src="{{ url_for('static', filename='js/realtime-progress.js') }}"></script>

    <!-- Main JavaScript - Auto-hiding filter bar and UI enhancements -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
