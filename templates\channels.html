<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Channels Dashboard - My YouTube Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet">
</head>
<body>
    <!-- Universal Command Center - Unified with Videos Page -->
    <header class="command-center">
        <div class="command-center-content">
            <!-- Simplified Centered Layout -->
            <div class="toolbar-main">
                <!-- Add Channel Form (Primary Action) -->
                <form action="/add_channel" method="POST" class="add-video-form">
                    <!-- Hidden input to preserve sorting state -->
                    <input type="hidden" name="sort_by" value="{{ sort_by }}">

                    <div class="search-input-container">
                        <span class="material-symbols-outlined search-icon-left">add</span>
                        <input type="text"
                               name="channel_url"
                               placeholder="Add channel by URL (e.g., https://www.youtube.com/@channelname)"
                               class="main-search-input">
                        <button type="submit" class="search-submit-button">
                            <span class="material-symbols-outlined">add</span>
                        </button>
                    </div>
                </form>

                <!-- Command Actions -->
                <div class="command-actions">
                    <!-- Page Title with Count -->
                    <div class="page-title-section">
                        <h1 class="page-title">
                            <span class="material-symbols-outlined">account_circle</span>
                            Channels ({{ channels|length }})
                        </h1>
                    </div>

                    <!-- Sorting Controls -->
                    <div class="sort-controls-wrapper">
                        <label for="sort-select" class="sort-label">Sort:</label>
                        <select id="sort-select" class="command-select" onchange="updateSort()">
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name (A-Z)</option>
                            <option value="subscribers" {% if sort_by == 'subscribers' %}selected{% endif %}>Subscribers</option>
                            <option value="videos" {% if sort_by == 'videos' %}selected{% endif %}>Video Count</option>
                        </select>
                    </div>

                    <!-- Navigation -->
                    <a href="/" class="command-action-button nav-action">
                        <span class="material-symbols-outlined">video_library</span>
                        Videos
                    </a>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">

            <!-- Channels Content -->
            {% if channels %}
                <div class="channels-content">
                    <div class="channels-grid">
                    {% for channel in channels %}
                    <div class="channel-card">
                        <div class="channel-header">
                            <img src="{{ channel.logoUrl }}"
                                 alt="{{ channel.name }}"
                                 class="channel-logo"
                                 loading="lazy"
                                 onerror="this.src='https://www.youtube.com/img/desktop/yt_1200.png'">
                            
                            <div class="channel-info">
                                <h3 class="channel-name">
                                    <a href="{% if channel.channelId.startswith('UC') %}https://www.youtube.com/channel/{{ channel.channelId }}{% else %}https://www.youtube.com/@{{ channel.channelId }}{% endif %}" target="_blank" rel="noopener noreferrer">
                                        {{ channel.name }}
                                    </a>
                                </h3>
                                <p class="channel-subscribers">{{ channel.subscriberCount }}</p>
                            </div>
                        </div>
                        
                        <div class="channel-stats">
                            <div class="stat-item">
                                <span class="material-symbols-outlined">video_library</span>
                                <span class="stat-value">{{ channel.videoCount }} video{{ 's' if channel.videoCount != 1 else '' }}</span>
                            </div>

                            <!-- Enhanced Statistics -->
                            {% if channel.totalVideos and channel.totalVideos > 0 %}
                            <div class="stat-item">
                                <span class="material-symbols-outlined">movie</span>
                                <span class="stat-value">{{ channel.totalVideos }} total videos</span>
                            </div>
                            {% endif %}

                            {% if channel.totalViews and channel.totalViews != 'Unknown' %}
                            <div class="stat-item">
                                <span class="material-symbols-outlined">visibility</span>
                                <span class="stat-value">{{ channel.totalViews }} total views</span>
                            </div>
                            {% endif %}

                            <div class="stat-item">
                                <span class="material-symbols-outlined">schedule</span>
                                <span class="stat-value">Updated {{ channel.lastUpdated[:10] }}</span>
                            </div>
                        </div>
                        
                        <div class="channel-actions">
                            <a href="/?channel_id={{ channel.channelId }}&group_filter=all&sort_by=channel" class="action-button">
                                <span class="material-symbols-outlined">filter_list</span>
                                View Videos
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                    </div>

                    <!-- Pagination Controls -->
                    {% set endpoint = 'channels' %}
                    {% include 'pagination_controls.html' %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-content">
                        <span class="material-symbols-outlined empty-icon">account_circle</span>
                        <h2>No channels found</h2>
                        <p>Add some YouTube videos first to see channel information!</p>
                        <a href="/" class="empty-action-button">
                            <span class="material-symbols-outlined">add</span>
                            Add Videos
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </main>

    <script>
        function updateSort() {
            const sortSelect = document.getElementById('sort-select');
            const sortBy = sortSelect.value;
            const url = new URL(window.location);
            url.searchParams.set('sort_by', sortBy);
            window.location.href = url.toString();
        }

        // Add keyboard shortcut (Ctrl+K or Cmd+K) to focus add channel input
        document.addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const channelInput = document.querySelector('input[name="channel_url"]');
                if (channelInput) {
                    channelInput.focus();
                    channelInput.select();
                }
            }
        });
    </script>
</body>
</html>
