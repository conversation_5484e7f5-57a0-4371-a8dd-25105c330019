/**
 * Main JavaScript functionality for YouTube Dashboard
 * Includes auto-hiding filter bar and other UI enhancements
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize auto-hiding filter bar
    initAutoHidingFilterBar();

    // Initialize real-time granular updates
    initRealTimeUpdates();
});

/**
 * Auto-hiding command center functionality
 * Hides the command center when scrolling down, shows it when scrolling up
 */
function initAutoHidingFilterBar() {
    const commandCenter = document.querySelector('.command-center');
    if (!commandCenter) return;

    let lastScrollTop = 0;
    const threshold = 150; // Pixels to scroll before hiding starts
    let ticking = false;

    function updateFilterBar() {
        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Only act if user scrolled more than 5 pixels (prevents jitter)
        if (Math.abs(currentScrollTop - lastScrollTop) <= 5) {
            ticking = false;
            return;
        }

        if (currentScrollTop > lastScrollTop && currentScrollTop > threshold) {
            // Scrolling Down - Hide command center
            commandCenter.classList.add('filter-bar--hidden');
        } else {
            // Scrolling Up - Show command center
            commandCenter.classList.remove('filter-bar--hidden');
        }

        lastScrollTop = currentScrollTop <= 0 ? 0 : currentScrollTop;
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateFilterBar);
            ticking = true;
        }
    }

    // Use throttled scroll event for better performance
    window.addEventListener('scroll', requestTick, { passive: true });
}

/**
 * Utility function to preserve scroll position during form submissions
 * This helps maintain user context when filters are applied
 */
function preserveScrollPosition() {
    // Store scroll position in localStorage
    localStorage.setItem('scrollPosition', window.pageYOffset.toString());
}

/**
 * Restore scroll position after page load
 * Called automatically on DOMContentLoaded
 */
function restoreScrollPosition() {
    const scrollPosition = localStorage.getItem('scrollPosition');
    if (scrollPosition) {
        window.scrollTo(0, parseInt(scrollPosition));
        localStorage.removeItem('scrollPosition');
    }
}

/**
 * Elite-Tier Real-Time Updates System with Numerical Progress Feedback
 * Updates individual video cards and provides precise progress information
 */
function initRealTimeUpdates() {
    // Only run if there are pending videos
    if (!document.querySelector('.video-card-pending')) {
        return;
    }

    console.log('🔄 Starting elite-tier real-time updates with numerical feedback...');

    // Create persistent progress toast
    const progressToast = createProgressToast();

    function pollForUpdates() {
        fetch('/scraping_status')
            .then(response => response.json())
            .then(data => {
                // Update progress bar with precise percentage
                if (data.pending_count > 0 && data.batch_size > 0) {
                    const progressRatio = data.progress_percentage / 100;
                    if (window.NProgress) {
                        NProgress.set(Math.max(0.1, progressRatio));
                    }

                    // Update numerical progress feedback
                    updateProgressToast(progressToast, data);
                }

                // Process completed videos for individual updates
                if (data.completed_videos && data.completed_videos.length > 0) {
                    console.log(`📦 Received ${data.completed_videos.length} completed videos`);

                    data.completed_videos.forEach(video => {
                        console.log(`✅ Completed: ${video.title}`);
                        updateVideoCard(video);
                    });

                    // Update progress toast immediately after processing completions
                    updateProgressToast(progressToast, data);
                }

                // Continue polling if there are still pending videos
                if (data.pending_count > 0) {
                    setTimeout(pollForUpdates, 2000); // Poll every 2 seconds
                } else {
                    console.log('✅ All videos completed!');
                    if (window.NProgress) {
                        NProgress.done();
                    }

                    // Show completion notification and remove progress toast
                    showCompletionToast(data.batch_size || data.completed_in_batch);
                    removeProgressToast(progressToast);
                }
            })
            .catch(error => {
                console.error('Error polling for updates:', error);
                updateProgressToast(progressToast, { error: true });
                // Retry after a longer delay on error
                setTimeout(pollForUpdates, 5000);
            });
    }

    // Start polling
    pollForUpdates();
}

/**
 * Updates a single video card from pending to completed state
 */
function updateVideoCard(videoData) {
    const pendingCard = document.querySelector(`.video-card-pending[data-video-id="${videoData.id}"]`);
    if (!pendingCard) {
        console.warn(`No pending card found for video ID: ${videoData.id}`);
        return;
    }

    console.log(`🔄 Updating card for: ${videoData.title}`);

    // Create the completed video card HTML
    const completedCardHTML = createVideoCardHTML(videoData);

    // Replace the pending card with the completed card
    pendingCard.outerHTML = completedCardHTML;

    // Add a subtle animation to highlight the update
    const newCard = document.querySelector(`[data-video-id="${videoData.id}"]`);
    if (newCard) {
        newCard.style.transform = 'scale(1.02)';
        newCard.style.transition = 'transform 0.3s ease';
        setTimeout(() => {
            newCard.style.transform = 'scale(1)';
        }, 300);
    }
}

/**
 * Creates HTML for a completed video card
 */
function createVideoCardHTML(video) {
    // Get current URL parameters for state preservation
    const urlParams = new URLSearchParams(window.location.search);
    const sortBy = urlParams.get('sort_by') || 'date_added_newest';
    const groupFilter = urlParams.get('group_filter') || 'all';
    const groupingFilter = urlParams.get('grouping_filter') || 'all';

    // Extract video ID from URL for remove link
    const videoId = video.url.includes('v=') ? video.url.split('v=')[1].split('&')[0] : video.id;

    // Generate group pills HTML
    let groupPillsHTML = '';
    if (video.groupDetails && video.groupDetails.length > 0) {
        groupPillsHTML = `
            <div class="group-pills">
                ${video.groupDetails.map(group => `
                    <a href="/?group_filter=${group.id}&sort_by=${sortBy}&grouping_filter=${groupingFilter}" class="group-link">
                        <span class="group-pill">${group.name}</span>
                    </a>
                `).join('')}
            </div>
        `;
    }

    return `
        <div class="video-card" data-status="complete" data-video-id="${video.id}">
            <div class="thumbnail-container">
                <a href="${video.url}" target="_blank" rel="noopener noreferrer">
                    <img src="${video.thumbnail}"
                         alt="Thumbnail for ${video.title}"
                         class="thumbnail"
                         loading="lazy">
                </a>
                <div class="video-length-badge">${video.length || 'N/A'}</div>

                <div class="video-actions">
                    <button class="group-manage-button"
                            onclick="openGroupModal('${video.id}')"
                            title="Manage groups">
                        <span class="material-symbols-outlined">folder_open</span>
                    </button>
                    <a href="/remove_video/${videoId}?sort_by=${sortBy}&group_filter=${groupFilter}&grouping_filter=${groupingFilter}"
                       class="remove-button"
                       onclick="return confirm('Remove this video from your feed?')"
                       title="Remove video">
                        <span class="material-symbols-outlined">close</span>
                    </a>
                </div>
            </div>

            <div class="video-info">
                <img src="${video.channelIconUrl || 'https://www.youtube.com/img/desktop/yt_1200.png'}"
                     alt="${video.channelName}"
                     class="channel-icon"
                     loading="lazy"
                     onerror="this.src='https://www.youtube.com/img/desktop/yt_1200.png'">

                <div class="video-text">
                    <h3 class="video-title">
                        <a href="${video.url}" target="_blank" rel="noopener noreferrer">
                            ${video.title}
                        </a>
                    </h3>
                    <p class="channel-name">
                        <a href="${video.channelUrl || '#'}" target="_blank" rel="noopener noreferrer" class="channel-link">
                            ${video.channelName}
                        </a>
                    </p>

                    ${groupPillsHTML}

                    <p class="video-meta">${video.views || 'N/A'} • ${video.uploadDate || 'Unknown date'}${video.timeAgo ? ` (${video.timeAgo})` : ''}</p>
                </div>
            </div>
        </div>
    `;
}

/**
 * Creates a persistent progress toast for numerical feedback
 */
function createProgressToast() {
    const toast = document.createElement('div');
    toast.className = 'progress-toast';
    toast.innerHTML = `
        <div class="progress-toast-content">
            <span class="material-symbols-outlined progress-icon">hourglass_empty</span>
            <div class="progress-text">
                <div class="progress-title">Scraping videos...</div>
                <div class="progress-details">Initializing...</div>
            </div>
        </div>
    `;

    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #1a1a1a;
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        border: 1px solid #3d3d3d;
        box-shadow: 0 8px 24px rgba(0,0,0,0.4);
        z-index: 10000;
        min-width: 280px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    // Style the content
    const content = toast.querySelector('.progress-toast-content');
    content.style.cssText = `
        display: flex;
        align-items: center;
        gap: 12px;
    `;

    const icon = toast.querySelector('.progress-icon');
    icon.style.cssText = `
        font-size: 24px;
        color: #065fd4;
        animation: spin 2s linear infinite;
    `;

    const title = toast.querySelector('.progress-title');
    title.style.cssText = `
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 4px;
    `;

    const details = toast.querySelector('.progress-details');
    details.style.cssText = `
        font-size: 12px;
        color: #aaa;
    `;

    // Add spin animation
    if (!document.querySelector('#progress-animations')) {
        const style = document.createElement('style');
        style.id = 'progress-animations';
        style.textContent = `
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    return toast;
}

/**
 * Updates the progress toast with current status
 */
function updateProgressToast(toast, data) {
    if (!toast) return;

    const title = toast.querySelector('.progress-title');
    const details = toast.querySelector('.progress-details');
    const icon = toast.querySelector('.progress-icon');

    if (data.error) {
        title.textContent = 'Connection error';
        details.textContent = 'Retrying...';
        icon.textContent = 'error';
        icon.style.color = '#ff6b6b';
        return;
    }

    if (data.batch_size > 0) {
        // RESTORED: Precise "X / Y" progress feedback
        const completed = data.completed_in_batch || (data.batch_size - data.pending_count);
        title.textContent = `Scraping: ${completed} / ${data.batch_size} complete`;
        details.textContent = `${data.progress_percentage}% • ${data.pending_count} remaining`;
        icon.textContent = 'hourglass_empty';
        icon.style.color = '#065fd4';
    }
}

/**
 * Removes the progress toast
 */
function removeProgressToast(toast) {
    if (!toast) return;

    toast.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
}

/**
 * Shows a completion toast notification
 */
function showCompletionToast(totalProcessed = 0) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'completion-toast';
    toast.innerHTML = `
        <span class="material-symbols-outlined">check_circle</span>
        <div>
            <div>Scraping Complete!</div>
            <div style="font-size: 12px; opacity: 0.8;">Processed ${totalProcessed} video${totalProcessed !== 1 ? 's' : ''}</div>
        </div>
    `;

    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: #4caf50;
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.4);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 500;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        min-width: 280px;
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 4 seconds
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

// Preserve scroll position on form submissions
document.addEventListener('DOMContentLoaded', () => {
    // Restore scroll position if available
    restoreScrollPosition();

    // Add event listeners to forms to preserve scroll position
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', preserveScrollPosition);
    });
});
