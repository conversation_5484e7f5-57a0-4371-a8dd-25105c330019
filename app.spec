# -*- mode: python ; coding: utf-8 -*-
# Updated PyInstaller spec file for YouTube Competitor Tool
# This configuration creates a single executable with all dependencies bundled

a = Analysis(
    ['app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),                    # Python modules
        ('templates', 'templates'),        # HTML templates
        ('static', 'static'),             # CSS, JS, and other static files
        ('videos.json', '.'),             # Video cache (if exists)
        ('channels.json', '.'),           # Channel cache (if exists)
    ],
    hiddenimports=[
        'src.data_manager',
        'src.scraper',
        'src.utils',
        'requests',
        'beautifulsoup4',
        'bs4',
        'flask',
        'json',
        'csv',
        'threading',
        'datetime',
        'time',
        'io',
        'os',
        'sys'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MyYouTubeFeed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,                        # No console window for clean user experience
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
