🎥 YouTube Competitor Analysis Tool - User Guide
==================================================

Hey there! 👋

This is a powerful YouTube analysis tool that helps you track and analyze YouTube videos and channels. It's been packaged as a single executable file for easy use.

📋 WHAT THIS TOOL DOES:
-----------------------
✅ Track YouTube videos from multiple channels
✅ Organize videos into custom groups/categories  
✅ Beautiful, modern web interface
✅ Export data to CSV/JSON formats
✅ Check for dead/deleted video links
✅ View detailed channel information
✅ Search and filter your video library
✅ Automatic data caching for fast performance

🚀 HOW TO USE IT:
-----------------

1. **Run the Application:**
   - Double-click "MyYouTubeFeed.exe" to start the server
   - Nothing will appear on your screen - that's normal! 
   - The app runs silently in the background

2. **Access the Web Interface:**
   - Open your web browser (Chrome, Firefox, Edge, etc.)
   - Go to: http://127.0.0.1:5000
   - You should see the YouTube tool interface!

3. **Start Adding Content:**
   - Click "Add Video" to add individual YouTube videos
   - Use "Add Channel" to track entire YouTube channels
   - Create groups to organize your content
   - Use the search and filter features to find specific videos

4. **Key Features:**
   - 🏠 Home: View all your tracked videos
   - 📺 Channels: Manage your tracked YouTube channels
   - 📊 Export: Download your data as CSV or JSON
   - 🔍 Search: Find specific videos quickly
   - 🏷️ Groups: Organize videos by category

💡 TIPS:
--------
- The tool saves all your data automatically
- You can close and reopen your browser - your data persists
- Use groups to organize videos by topic, priority, etc.
- The export feature is great for sharing data with others
- Check the "Dead Link Checker" to find removed videos

🛑 HOW TO CLOSE THE APPLICATION:
--------------------------------
- Simply close your browser tab when done
- The application continues running in the background
- To fully shut it down:
  1. Press Ctrl+Shift+Esc to open Task Manager
  2. Look for "MyYouTubeFeed.exe" in the process list
  3. Right-click and select "End Task"

⚠️ TROUBLESHOOTING:
-------------------
- If the browser shows "can't connect": Wait 10-15 seconds after starting the app
- If port 5000 is busy: Close other applications that might use this port
- If you see errors: Try running as Administrator (right-click → "Run as administrator")

📁 DATA STORAGE:
----------------
Your data is stored in JSON files next to the executable:
- videos.json: Your tracked videos
- channels.json: Your tracked channels

These files are automatically created and updated. You can back them up if needed!

🎯 GETTING STARTED QUICKLY:
---------------------------
1. Start MyYouTubeFeed.exe
2. Open browser → http://127.0.0.1:5000
3. Click "Add Video" and paste a YouTube URL
4. Explore the interface and features!

Need help? The interface is intuitive and includes helpful tooltips throughout.

Enjoy tracking your YouTube content! 🎉

---
Built with ❤️ using Python, Flask, and modern web technologies
