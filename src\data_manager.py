"""
Data Management Module

This module handles all interactions with JSON cache files and data operations.
Provides functions for loading, saving, and manipulating video and channel data.
"""

import os
import json
import uuid
import time
import tempfile
from datetime import datetime, timezone


# --- CONFIGURATION ---
VIDEO_DATA_FILE = 'videos.json'
CHANNEL_DATA_FILE = 'channels.json'

# --- FILE LOCKING UTILITIES ---

def _acquire_file_lock(file_path, max_wait_seconds=10):
    """
    Acquires a file lock to prevent concurrent writes.
    Returns True if lock acquired, False if timeout.
    """
    lock_file = f"{file_path}.lock"
    start_time = time.time()

    while time.time() - start_time < max_wait_seconds:
        try:
            # Try to create lock file exclusively (fails if exists)
            with open(lock_file, 'x', encoding='utf-8') as f:
                f.write(str(os.getpid()))  # Write process ID for debugging
            return True
        except FileExistsError:
            # Lock exists, wait a bit and retry
            time.sleep(0.1)

    print(f"⚠️ Failed to acquire lock for {file_path} after {max_wait_seconds}s")
    return False


def _release_file_lock(file_path):
    """Releases a file lock by removing the lock file."""
    lock_file = f"{file_path}.lock"
    try:
        if os.path.exists(lock_file):
            os.remove(lock_file)
    except OSError as e:
        print(f"⚠️ Error releasing lock for {file_path}: {e}")


def _safe_write_json(file_path, data):
    """
    Thread-safe JSON writing with file locking and atomic operations.
    Prevents data corruption from concurrent writes.
    """
    # Step 1: Acquire file lock
    if not _acquire_file_lock(file_path):
        raise RuntimeError(f"Could not acquire lock for {file_path}")

    try:
        # Step 2: Read current data to merge any concurrent changes
        current_data = {}
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    current_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                current_data = {}

        # Step 3: Merge data intelligently
        if isinstance(data, dict) and isinstance(current_data, dict):
            # For structured data, merge at top level
            merged_data = current_data.copy()
            merged_data.update(data)
            final_data = merged_data
        else:
            # For simple data, use new data
            final_data = data

        # Step 4: Atomic write using temporary file
        temp_dir = os.path.dirname(file_path) or '.'
        with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False,
                                       dir=temp_dir, suffix='.tmp') as temp_file:
            json.dump(final_data, temp_file, indent=2, ensure_ascii=False)
            temp_file_path = temp_file.name

        # Step 5: Atomic rename (works on most filesystems)
        if os.name == 'nt':  # Windows
            if os.path.exists(file_path):
                os.remove(file_path)
        os.rename(temp_file_path, file_path)

        return final_data

    finally:
        # Step 6: Always release the lock
        _release_file_lock(file_path)


# --- VIDEO DATA FUNCTIONS ---

def load_data():
    """Loads the complete data structure (videos and groups) from JSON file."""
    if not os.path.exists(VIDEO_DATA_FILE):
        return {'videos': [], 'groups': []}
    try:
        with open(VIDEO_DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # Handle migration from old formats
            if 'video_ids' in data:
                print("Migrating from old video_ids format to new structure...")
                return {'videos': [], 'groups': []}
            elif isinstance(data.get('videos'), list) and 'groups' not in data:
                print("Migrating from cache-only format to groups structure...")
                # Migrate existing videos to new format
                videos = data.get('videos', [])
                for video in videos:
                    if 'groups' not in video:
                        video['groups'] = []  # Add empty groups list
                return {'videos': videos, 'groups': []}
            return {
                'videos': data.get('videos', []),
                'groups': data.get('groups', [])
            }
    except (json.JSONDecodeError, FileNotFoundError):
        return {'videos': [], 'groups': []}


def save_data(data):
    """
    Thread-safe save of the complete data structure to JSON file.
    Uses file locking to prevent data corruption from concurrent writes.
    """
    try:
        _safe_write_json(VIDEO_DATA_FILE, data)
        print(f"✅ Safely saved data structure with {len(data.get('videos', []))} videos and {len(data.get('groups', []))} groups")
    except Exception as e:
        print(f"💥 Error saving data: {e}")
        raise


def load_video_cache():
    """Backward compatibility function - loads just videos."""
    data = load_data()
    return data['videos']


def save_video_cache(videos):
    """
    Thread-safe backward compatibility function - saves just videos.
    Preserves existing groups data while updating videos.
    """
    # Load current data to preserve groups
    current_data = load_data()
    current_data['videos'] = videos
    save_data(current_data)


def find_video_by_id(videos, video_id):
    """Finds a video object by its ID in the cache."""
    for video in videos:
        if video.get('id') == video_id:
            return video
    return None


def find_group_by_id(groups, group_id):
    """Finds a group object by its ID."""
    for group in groups:
        if group.get('id') == group_id:
            return group
    return None


def create_group(name):
    """Creates a new group object with unique ID."""
    return {
        'id': str(uuid.uuid4()),
        'name': name,
        'created': datetime.now(timezone.utc).isoformat()
    }


def filter_videos_by_group(videos, group_id):
    """Filters videos to only include those in the specified group."""
    if not group_id or group_id == 'all':
        return videos
    return [video for video in videos if group_id in video.get('groups', [])]


def filter_videos_by_channel(videos, channel_id):
    """Filters videos to only include those from the specified channel."""
    if not channel_id or channel_id == 'all':
        return videos

    # Filter by channelId (preferred) or fallback to channelName for backward compatibility
    filtered_videos = []
    for video in videos:
        video_channel_id = video.get('channelId', '')
        video_channel_name = video.get('channelName', '')

        # Try to match by channelId first (most reliable)
        if video_channel_id and video_channel_id == channel_id:
            filtered_videos.append(video)
        # Fallback to channelName matching if channelId not available
        elif not video_channel_id and video_channel_name == channel_id:
            filtered_videos.append(video)

    return filtered_videos


# --- CHANNEL DATA FUNCTIONS ---

def load_channel_data():
    """Loads the channel data cache from JSON file."""
    if not os.path.exists(CHANNEL_DATA_FILE):
        return {}
    try:
        with open(CHANNEL_DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {}


def save_channel_data(channel_data):
    """
    Thread-safe save of the channel data cache to JSON file.
    Uses file locking to prevent data corruption from concurrent writes.
    """
    try:
        _safe_write_json(CHANNEL_DATA_FILE, channel_data)
        print(f"✅ Safely saved channel data with {len(channel_data)} channels")
    except Exception as e:
        print(f"💥 Error saving channel data: {e}")
        raise


def find_channel_by_name(channel_data, channel_name):
    """Finds a channel object by its name in the cache."""
    for channel_id, channel in channel_data.items():
        if channel.get('name') == channel_name:
            return channel
    return None


def is_channel_data_stale(channel, max_age_hours=24):
    """Checks if channel data is older than max_age_hours and needs refreshing."""
    if not channel.get('lastUpdated'):
        return True
    
    try:
        last_updated = datetime.fromisoformat(channel['lastUpdated'].replace('Z', '+00:00'))
        now = datetime.now(timezone.utc)
        age_hours = (now - last_updated).total_seconds() / 3600
        return age_hours > max_age_hours
    except (ValueError, TypeError):
        return True


def get_unique_channels_from_videos(videos):
    """Extracts unique channel names from video data."""
    channels = set()
    for video in videos:
        channel_name = video.get('channelName')
        if channel_name and channel_name != 'Unknown Channel':
            channels.add(channel_name)
    return list(channels)


def get_canonical_channel_identifiers_from_videos(videos):
    """
    Extracts canonical channel identifiers from video data.
    Returns a set of tuples: (channelId, channelName) for robust identification.
    """
    channel_identifiers = set()
    for video in videos:
        channel_id = video.get('channelId', '')
        channel_name = video.get('channelName', '')

        if channel_name and channel_name != 'Unknown Channel':
            # Use channelId if available, otherwise use channelName as identifier
            identifier = channel_id if channel_id else channel_name
            channel_identifiers.add((identifier, channel_name))

    return channel_identifiers


def purge_stale_channels_from_cache(channel_cache, canonical_identifiers):
    """
    Removes channels from cache that are no longer present in any videos.
    Returns the cleaned cache.
    """
    # Create lookup sets for efficient checking
    canonical_ids = {identifier for identifier, _ in canonical_identifiers}
    canonical_names = {name for _, name in canonical_identifiers}

    # Keep only channels that exist in our canonical list
    cleaned_cache = {}
    for cache_key, channel_data in channel_cache.items():
        channel_id = channel_data.get('channelId', '')
        channel_name = channel_data.get('name', '')

        # Keep channel if its ID or name is in our canonical list
        if channel_id in canonical_ids or channel_name in canonical_names or cache_key in canonical_names:
            cleaned_cache[cache_key] = channel_data
        else:
            print(f"Purging stale channel from cache: {channel_name} (ID: {channel_id})")

    return cleaned_cache
