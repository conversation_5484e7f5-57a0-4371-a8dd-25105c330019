"""
Utility Functions Module

This module contains small, reusable helper functions for formatting,
calculations, and other common operations.
"""

import os
import sys
from datetime import datetime, timezone


def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON><PERSON><PERSON>nstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # If not running as a PyInstaller bundle, use the normal path
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)


def format_view_count(view_count_str):
    """Formats the view count into a human-readable string."""
    try:
        # Remove commas and convert to int
        view_count = int(view_count_str.replace(',', '').replace(' views', ''))

        if view_count >= 1_000_000_000:
            return f"{view_count / 1_000_000_000:.1f}B views"
        elif view_count >= 1_000_000:
            return f"{view_count / 1_000_000:.1f}M views"
        elif view_count >= 1_000:
            return f"{view_count / 1_000:.1f}K views"
        else:
            return f"{view_count:,} views"
    except (ValueError, AttributeError):
        return view_count_str


def format_duration(seconds_str):
    """Converts duration from seconds to MM:SS format."""
    try:
        total_seconds = int(seconds_str)
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        return f"{minutes}:{seconds:02d}"
    except (ValueError, TypeError):
        return "0:00"


def calculate_time_ago(date_str):
    """Calculates relative time from a date string (YYYY-MM-DD format)."""
    try:
        # Parse the date string
        upload_date = datetime.strptime(date_str, "%Y-%m-%d")
        # Make it timezone aware (assume UTC)
        upload_date = upload_date.replace(tzinfo=timezone.utc)

        # Get current time in UTC
        now = datetime.now(timezone.utc)

        # Calculate the difference
        diff = now - upload_date

        # Convert to total seconds for easier calculation
        total_seconds = int(diff.total_seconds())

        # Calculate time units
        if total_seconds < 60:
            return "just now"
        elif total_seconds < 3600:  # Less than 1 hour
            minutes = total_seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif total_seconds < 86400:  # Less than 1 day
            hours = total_seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif total_seconds < 604800:  # Less than 1 week
            days = total_seconds // 86400
            return f"{days} day{'s' if days != 1 else ''} ago"
        elif total_seconds < 2629746:  # Less than 1 month (30.44 days average)
            weeks = total_seconds // 604800
            return f"{weeks} week{'s' if weeks != 1 else ''} ago"
        elif total_seconds < 31556952:  # Less than 1 year (365.24 days)
            months = total_seconds // 2629746
            return f"{months} month{'s' if months != 1 else ''} ago"
        else:
            years = total_seconds // 31556952
            return f"{years} year{'s' if years != 1 else ''} ago"

    except (ValueError, TypeError):
        return ""


def format_subscriber_count(sub_count_str):
    """Formats subscriber count into a human-readable string."""
    if not sub_count_str:
        return "Unknown"
    
    # Remove common suffixes and clean the string
    clean_str = sub_count_str.lower().replace('subscribers', '').replace('subscriber', '').strip()
    
    try:
        # Handle different formats like "1.2M", "500K", "1,234"
        if 'k' in clean_str:
            num = float(clean_str.replace('k', ''))
            return f"{num:.1f}K subscribers" if num != int(num) else f"{int(num)}K subscribers"
        elif 'm' in clean_str:
            num = float(clean_str.replace('m', ''))
            return f"{num:.1f}M subscribers" if num != int(num) else f"{int(num)}M subscribers"
        elif 'b' in clean_str:
            num = float(clean_str.replace('b', ''))
            return f"{num:.1f}B subscribers" if num != int(num) else f"{int(num)}B subscribers"
        else:
            # Handle comma-separated numbers
            num = int(clean_str.replace(',', ''))
            if num >= 1_000_000_000:
                return f"{num / 1_000_000_000:.1f}B subscribers"
            elif num >= 1_000_000:
                return f"{num / 1_000_000:.1f}M subscribers"
            elif num >= 1_000:
                return f"{num / 1_000:.1f}K subscribers"
            else:
                return f"{num:,} subscribers"
    except (ValueError, AttributeError):
        return sub_count_str


def update_video_time_ago(video):
    """Updates the timeAgo field for a cached video object based on its publishDateRaw."""
    if video.get('publishDateRaw'):
        try:
            # Parse the ISO format date
            publish_date = datetime.fromisoformat(video['publishDateRaw'].replace('Z', '+00:00'))
            date_str = publish_date.strftime("%Y-%m-%d")
            video['timeAgo'] = calculate_time_ago(date_str)
        except (ValueError, TypeError):
            video['timeAgo'] = ''
    else:
        video['timeAgo'] = ''
    return video


def prepare_videos_for_display(videos, groups=None):
    """Prepares cached video objects for display by updating time-sensitive fields and adding group names."""
    # Import here to avoid circular imports
    from src.data_manager import load_channel_data, find_channel_by_name

    display_videos = []

    # Create group lookup map for efficient group name resolution
    group_map = {}
    if groups:
        group_map = {group['id']: group['name'] for group in groups}

    # Load channel cache for accurate URL generation
    channel_cache = load_channel_data()

    for video in videos:
        # Create a copy to avoid modifying the original cache
        display_video = video.copy()
        # Update the relative time
        display_video = update_video_time_ago(display_video)

        # Add clickable channel URL for video cards using CHANNEL CACHE (SINGLE SOURCE OF TRUTH)
        channel_name = video.get('channelName', '')
        channel_id = video.get('channelId', '')

        # First, try to find the channel in our cache by name (most reliable)
        cached_channel = find_channel_by_name(channel_cache, channel_name) if channel_name else None

        if cached_channel and cached_channel.get('url'):
            # Use the cached channel URL (most reliable - from actual scraping)
            display_video['channelUrl'] = cached_channel['url']
        elif channel_id and channel_id.startswith('UC'):
            # Fallback 1: Use permanent channel ID URL format
            display_video['channelUrl'] = f'https://www.youtube.com/channel/{channel_id}'
        elif channel_id and not channel_id.startswith('UC'):
            # Fallback 2: Use @handle format for non-UC channel IDs
            display_video['channelUrl'] = f'https://www.youtube.com/@{channel_id}'
        elif channel_name and channel_name != 'Loading...' and channel_name != 'Unknown Channel':
            # Fallback 3: construct URL from channel name
            display_video['channelUrl'] = f'https://www.youtube.com/@{channel_name}'
        else:
            # No valid channel info available
            display_video['channelUrl'] = '#'

        # Add group details for display on cards (both ID and name for clickable links)
        if group_map:
            display_video['groupDetails'] = [
                {'id': group_id, 'name': group_map[group_id]}
                for group_id in video.get('groups', [])
                if group_id in group_map
            ]
            # Keep groupNames for backward compatibility
            display_video['groupNames'] = [
                group_map[group_id] for group_id in video.get('groups', [])
                if group_id in group_map
            ]
        else:
            display_video['groupDetails'] = []
            display_video['groupNames'] = []

        display_videos.append(display_video)
    return display_videos


def check_video_status(video_id):
    """
    Check the availability status of a YouTube video.

    This function makes a lightweight request to determine if a video is:
    - LIVE: Video is publicly available
    - DELETED: Video has been removed from YouTube
    - PRIVATE: Video exists but is set to private/unlisted
    - ERROR: Unable to determine status due to network issues

    Args:
        video_id (str): YouTube video ID

    Returns:
        str: Status code ('LIVE', 'DELETED', 'PRIVATE', 'ERROR')
    """
    import requests
    import time

    video_url = f'https://www.youtube.com/watch?v={video_id}'

    try:
        # Use a lightweight HEAD request first to check basic availability
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        print(f"  🔍 Checking video {video_id}...")

        # Make the request with a reasonable timeout
        response = requests.get(video_url, headers=headers, timeout=10)

        # Check response status code
        if response.status_code == 404:
            print(f"    ❌ Video {video_id}: DELETED (404)")
            return 'DELETED'
        elif response.status_code != 200:
            print(f"    ⚠ Video {video_id}: ERROR (HTTP {response.status_code})")
            return 'ERROR'

        # Analyze page content for more specific status
        page_content = response.text.lower()

        # Check for specific YouTube error messages
        deleted_indicators = [
            'video unavailable',
            'this video has been removed',
            'this video is no longer available',
            'video has been removed by the user',
            'this video has been deleted',
            'content warning'
        ]

        private_indicators = [
            'video is private',
            'this video is private',
            'private video',
            'this video is unlisted',
            'video is unlisted'
        ]

        # Check for deletion indicators
        for indicator in deleted_indicators:
            if indicator in page_content:
                print(f"    ❌ Video {video_id}: DELETED ('{indicator}' found)")
                return 'DELETED'

        # Check for private indicators
        for indicator in private_indicators:
            if indicator in page_content:
                print(f"    🔒 Video {video_id}: PRIVATE ('{indicator}' found)")
                return 'PRIVATE'

        # Check if we can find video title and basic metadata (indicates live video)
        live_indicators = [
            '"videoid"',
            '"video_id"',
            'ytplayer.config',
            'ytInitialPlayerResponse'
        ]

        has_live_indicators = any(indicator in page_content for indicator in live_indicators)

        if has_live_indicators:
            print(f"    ✅ Video {video_id}: LIVE")
            return 'LIVE'
        else:
            # Ambiguous case - might be private or have restricted access
            print(f"    ⚠ Video {video_id}: PRIVATE (no clear indicators)")
            return 'PRIVATE'

    except requests.exceptions.Timeout:
        print(f"    ⏱ Video {video_id}: ERROR (timeout)")
        return 'ERROR'
    except requests.exceptions.ConnectionError:
        print(f"    🌐 Video {video_id}: ERROR (connection error)")
        return 'ERROR'
    except Exception as e:
        print(f"    ❌ Video {video_id}: ERROR ({str(e)})")
        return 'ERROR'


def audit_video_library(videos, progress_callback=None):
    """
    Audit an entire video library to check for dead links.

    This function checks the status of all videos and updates their status field.
    It includes progress tracking and respectful rate limiting.

    Args:
        videos (list): List of video objects to audit
        progress_callback (function): Optional callback for progress updates

    Returns:
        dict: Summary of audit results
    """
    import time

    print(f"🔍 Starting video library audit for {len(videos)} videos...")

    results = {
        'total': len(videos),
        'live': 0,
        'deleted': 0,
        'private': 0,
        'error': 0,
        'processed': 0
    }

    for i, video in enumerate(videos):
        video_id = video.get('id')
        if not video_id:
            continue

        # Check video status
        status = check_video_status(video_id)

        # Update video object with new status
        if status == 'LIVE':
            video['status'] = 'complete'  # Keep existing status for live videos
            results['live'] += 1
        elif status == 'DELETED':
            video['status'] = 'deleted'
            results['deleted'] += 1
        elif status == 'PRIVATE':
            video['status'] = 'private'
            results['private'] += 1
        else:  # ERROR
            video['status'] = 'error'
            results['error'] += 1

        results['processed'] += 1

        # Progress callback for real-time updates
        if progress_callback:
            progress_percentage = (results['processed'] / results['total']) * 100
            progress_callback(results['processed'], results['total'], progress_percentage, results)

        # Respectful rate limiting - 1 second delay between requests
        if i < len(videos) - 1:  # Don't delay after the last video
            time.sleep(1)

    print(f"✅ Audit complete!")
    print(f"   📊 Results: {results['live']} live, {results['deleted']} deleted, {results['private']} private, {results['error']} errors")

    return results
