"""
Web Scraping Module

This module contains all web scraping logic for YouTube videos and channels.
Handles network requests, HTML parsing, and data extraction.
"""

import re
import json
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timezone
from .utils import format_view_count, format_duration, calculate_time_ago, format_subscriber_count


def get_video_id(youtube_url):
    """Extracts the YouTube video ID from a URL."""
    patterns = [
        r'(?:https?:\/\/)?(?:www\.)?(?:youtube\.com|youtu\.be)\/(?:watch\?v=)?(?:embed\/)?(?:v\/)?(?:shorts\/)?([a-zA-Z0-9_-]{11})',
        r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})',
        r'(?:https?:\/\/)?youtu\.be\/([a-zA-Z0-9_-]{11})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, youtube_url)
        if match:
            return match.group(1)
    return None


def extract_channel_id_from_url(channel_url):
    """
    Extracts channel identifier from various YouTube channel URL formats.

    Supports:
    - https://www.youtube.com/@channelname
    - https://www.youtube.com/c/channelname
    - https://www.youtube.com/user/username
    - https://www.youtube.com/channel/UC...
    - @channelname (standalone)
    - channelname (plain text)
    """
    if not channel_url:
        return None

    # Clean the input
    channel_url = channel_url.strip()

    # Handle different channel URL formats with robust patterns
    patterns = [
        r'youtube\.com/channel/([^/?&\s]+)',           # /channel/UC... (more permissive)
        r'youtube\.com/c/([^/?&\s]+)',                 # /c/channelname
        r'youtube\.com/@([^/?&\s]+)',                  # /@channelname (handles special chars)
        r'youtube\.com/user/([^/?&\s]+)',              # /user/username
        r'^@([^/?&\s]+)$',                             # @channelname (standalone, more permissive)
        r'^([a-zA-Z0-9_-]+)$',                         # channelname (plain text, keep restrictive for safety)
    ]

    for pattern in patterns:
        match = re.search(pattern, channel_url, re.IGNORECASE)
        if match:
            return match.group(1)
    return None


def scrape_youtube_video_to_object(video_id):
    """
    Scrapes video details from YouTube and returns a complete video object for caching.
    This is the ONLY function that should make network requests for videos.
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    url = f"https://www.youtube.com/watch?v={video_id}"
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # ENHANCED 3-TIER WATERFALL DATE SCRAPING SYSTEM
        soup = BeautifulSoup(response.text, 'html.parser')
        precise_date = ""
        formatted_date = "Unknown"
        time_ago = ""
        publish_date_raw = None

        def parse_date_string(date_str, source="unknown"):
            """Helper function to parse various date formats and return standardized data."""
            if not date_str:
                return None, None, None
            
            # Try multiple date formats
            date_formats = [
                "%Y-%m-%d",           # 2023-05-14
                "%b %d, %Y",          # May 14, 2023
                "%d %b %Y",           # 14 May 2023
                "%Y-%m-%dT%H:%M:%S",  # 2023-05-14T10:30:00
                "%Y-%m-%dT%H:%M:%SZ", # 2023-05-14T10:30:00Z
            ]
            
            for date_format in date_formats:
                try:
                    date_obj = datetime.strptime(date_str.strip(), date_format)
                    formatted = date_obj.strftime("%b %d, %Y")
                    time_ago_calc = calculate_time_ago(date_obj.strftime("%Y-%m-%d"))
                    print(f"Successfully parsed date from {source}: {date_str} -> {formatted}")
                    return date_obj, formatted, time_ago_calc
                except ValueError:
                    continue
            return None, None, None

        # ATTEMPT 1 (PRIMARY): Parse ytInitialPlayerResponse JSON for most reliable date
        player_response_match = re.search(r'var ytInitialPlayerResponse = ({.*?});', response.text)
        if player_response_match and not publish_date_raw:
            try:
                data = json.loads(player_response_match.group(1))
                
                # Try videoDetails.publishDate first
                video_details = data.get('videoDetails', {})
                if 'publishDate' in video_details:
                    date_obj, formatted, time_ago_calc = parse_date_string(video_details['publishDate'], "ytInitialPlayerResponse.videoDetails.publishDate")
                    if date_obj:
                        publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc
                
                # Try microformat.playerMicroformatRenderer.publishDate
                if not publish_date_raw:
                    microformat = data.get('microformat', {}).get('playerMicroformatRenderer', {})
                    if 'publishDate' in microformat:
                        date_obj, formatted, time_ago_calc = parse_date_string(microformat['publishDate'], "microformat.publishDate")
                        if date_obj:
                            publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc
                
                # Try uploadDate as well
                if not publish_date_raw and 'uploadDate' in microformat:
                    date_obj, formatted, time_ago_calc = parse_date_string(microformat['uploadDate'], "microformat.uploadDate")
                    if date_obj:
                        publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc
                        
            except (json.JSONDecodeError, KeyError, IndexError) as e:
                print(f"Failed to parse ytInitialPlayerResponse for date: {e}")

        # ATTEMPT 2 (FALLBACK): Look for the datePublished meta tag
        if not publish_date_raw:
            date_meta = soup.find('meta', {'itemprop': 'datePublished'})
            if date_meta:
                date_content = date_meta.get('content', '')
                date_obj, formatted, time_ago_calc = parse_date_string(date_content, "meta[itemprop='datePublished']")
                if date_obj:
                    publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc

        # ATTEMPT 3 (FINAL FALLBACK): Regex search for date patterns in HTML
        if not publish_date_raw:
            print("Attempting regex fallback for date extraction...")
            # Common date patterns to search for
            date_patterns = [
                r'"publishDate":"([^"]+)"',                    # JSON publishDate
                r'"datePublished":"([^"]+)"',                  # JSON datePublished  
                r'datePublished["\s]*:["\s]*([^"]+)',         # Various JSON formats
                r'uploadDate["\s]*:["\s]*([^"]+)',            # uploadDate variants
                r'Published on ([A-Za-z]+ \d{1,2}, \d{4})',   # "Published on May 14, 2023"
                r'(\d{4}-\d{2}-\d{2})',                       # YYYY-MM-DD anywhere
                r'([A-Za-z]+ \d{1,2}, \d{4})',               # "May 14, 2023" format
            ]
            
            for pattern in date_patterns:
                matches = re.findall(pattern, response.text)
                for match in matches:
                    date_obj, formatted, time_ago_calc = parse_date_string(match, f"regex pattern: {pattern}")
                    if date_obj:
                        publish_date_raw, formatted_date, time_ago = date_obj, formatted, time_ago_calc
                        break
                if publish_date_raw:  # Break outer loop if date found
                    break

        # Now extract other video data using ytInitialPlayerResponse
        player_response_match = re.search(r'var ytInitialPlayerResponse = ({.*?});', response.text)
        channel_icon_url = ""
        title = ""
        view_count_raw = 0
        view_count = "0"
        channel_name = "Unknown Channel"
        channel_id = ""  # CRITICAL: Extract canonical channel ID
        length_seconds = "0"

        if player_response_match:
            try:
                data = json.loads(player_response_match.group(1))
                video_details = data.get('videoDetails', {})

                title = video_details.get('title', '')
                view_count_raw = int(video_details.get('viewCount', '0'))
                view_count = video_details.get('viewCount', '0')
                channel_name = video_details.get('author', 'Unknown Channel')

                # ENFORCE CANONICAL CHANNEL ID - ONLY UC... FORMAT ACCEPTED
                raw_channel_id = video_details.get('channelId', '')
                channel_id = ''  # Default to empty - no fallbacks allowed

                if raw_channel_id and raw_channel_id.startswith('UC') and len(raw_channel_id) == 24:
                    # Valid canonical channel ID format (UC + 22 characters)
                    channel_id = raw_channel_id
                    print(f"✓ Extracted CANONICAL channelId: {channel_id} for channel: {channel_name}")
                else:
                    # Reject any non-canonical identifiers - enforce data purity
                    print(f"⚠ REJECTED non-canonical channelId '{raw_channel_id}' for: {channel_name}")
                    print(f"  → Only UC... format (24 chars) accepted. Channel ID will be empty.")

                length_seconds = video_details.get('lengthSeconds', '0')

                # Try to find channel icon
                initial_data_match = re.search(r'var ytInitialData = ({.*?});', response.text)
                if initial_data_match:
                    try:
                        initial_data = json.loads(initial_data_match.group(1))

                        # Try to find channel icon
                        secondary_results = initial_data.get('contents', {}).get('twoColumnWatchNextResults', {}).get('results', {}).get('results', {}).get('contents', [])
                        for content in secondary_results:
                            if 'videoSecondaryInfoRenderer' in content:
                                owner = content['videoSecondaryInfoRenderer'].get('owner', {}).get('videoOwnerRenderer', {})
                                thumbnails = owner.get('thumbnail', {}).get('thumbnails', [])
                                if thumbnails:
                                    channel_icon_url = thumbnails[-1].get('url', '')
                                break

                    except (json.JSONDecodeError, KeyError, IndexError):
                        pass

                if title and view_count:
                    # Create complete video object for caching with CANONICAL CHANNEL ID
                    now = datetime.now(timezone.utc)
                    return {
                        'id': video_id,
                        'title': title,
                        'views': format_view_count(view_count),
                        'viewCountRaw': view_count_raw,
                        'thumbnail': f'https://i.ytimg.com/vi/{video_id}/hqdefault.jpg',
                        'url': url,
                        'channelName': channel_name,
                        'channelId': channel_id,  # CANONICAL CHANNEL ID - SINGLE SOURCE OF TRUTH
                        'channelIconUrl': channel_icon_url or f'https://i.ytimg.com/vi/{video_id}/default.jpg',
                        'uploadDate': formatted_date,
                        'publishDateRaw': publish_date_raw.isoformat() if publish_date_raw else None,
                        'timeAgo': time_ago,
                        'length': format_duration(length_seconds),
                        'groups': [],  # Initialize empty groups list
                        'dateAdded': now.isoformat(),
                        'lastUpdated': now.isoformat()
                    }
            except json.JSONDecodeError:
                pass
        
        # Method 2: Parse HTML with BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Try to find title
        title = None
        title_meta = soup.find('meta', property='og:title')
        if title_meta:
            title = title_meta.get('content')
        
        if not title:
            title_tag = soup.find('title')
            if title_tag:
                title = title_tag.text.replace(' - YouTube', '')
        
        # Try to find view count in various ways
        view_count = "0 views"
        
        # Look for view count in script tags
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                view_match = re.search(r'"viewCount":"(\d+)"', script.string)
                if view_match:
                    view_count = format_view_count(view_match.group(1))
                    break
                
                # Alternative pattern
                view_match = re.search(r'viewCount.*?(\d{1,3}(?:,\d{3})*)', script.string)
                if view_match:
                    view_count = format_view_count(view_match.group(1))
                    break
        
        # Fallback object creation
        now = datetime.now(timezone.utc)
        return {
            'id': video_id,
            'title': title or f'Video {video_id}',
            'views': view_count,
            'viewCountRaw': 0,
            'thumbnail': f'https://i.ytimg.com/vi/{video_id}/hqdefault.jpg',
            'url': url,
            'channelName': 'Unknown Channel',
            'channelId': '',  # No channel ID available in fallback
            'channelIconUrl': f'https://i.ytimg.com/vi/{video_id}/default.jpg',
            'uploadDate': formatted_date or 'Unknown',
            'publishDateRaw': publish_date_raw.isoformat() if publish_date_raw else None,
            'timeAgo': time_ago,
            'length': '0:00',
            'groups': [],  # Initialize empty groups list
            'dateAdded': now.isoformat(),
            'lastUpdated': now.isoformat()
        }

    except Exception as e:
        print(f"Error scraping video {video_id}: {e}")
        now = datetime.now(timezone.utc)
        return {
            'id': video_id,
            'title': f'Video {video_id}',
            'views': 'Views unavailable',
            'viewCountRaw': 0,
            'thumbnail': f'https://i.ytimg.com/vi/{video_id}/hqdefault.jpg',
            'url': url,
            'channelName': 'Unknown Channel',
            'channelId': '',  # No channel ID available in error case
            'channelIconUrl': f'https://i.ytimg.com/vi/{video_id}/default.jpg',
            'uploadDate': 'Unknown',
            'publishDateRaw': None,
            'timeAgo': '',
            'length': '0:00',
            'groups': [],  # Initialize empty groups list
            'dateAdded': now.isoformat(),
            'lastUpdated': now.isoformat()
        }


def scrape_playlist_for_video_ids(playlist_url):
    """
    ENHANCED ROBUST PLAYLIST SCRAPER with Unlisted Playlist Support

    Extracts video IDs from YouTube playlists using a comprehensive waterfall strategy
    designed to handle both public and unlisted playlists with maximum reliability.

    Strategy:
    1. Primary: ytInitialData JSON parsing (works for most public playlists)
    2. Fallback 1: playlistVideoListRenderer search (robust for unlisted playlists)
    3. Fallback 2: Enhanced regex patterns (catches edge cases)
    4. Final: Graceful failure with detailed error reporting

    Args:
        playlist_url (str): YouTube playlist URL (public or unlisted)

    Returns:
        list: List of video IDs found in the playlist, empty list if failed
    """
    print(f"🎵 Scraping playlist: {playlist_url}")

    try:
        # Extract playlist ID from URL
        playlist_id = None
        if 'list=' in playlist_url:
            playlist_id = playlist_url.split('list=')[1].split('&')[0]
        else:
            print("❌ Invalid playlist URL format")
            return []

        print(f"📋 Playlist ID: {playlist_id}")

        # Enhanced headers to appear as legitimate browser (critical for unlisted playlists)
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }

        print(f"🌐 Making request with enhanced browser headers...")
        response = requests.get(playlist_url, headers=headers, timeout=15)
        response.raise_for_status()

        # === WATERFALL STRATEGY IMPLEMENTATION ===
        video_ids = []

        # === METHOD 1: PRIMARY - ytInitialData JSON Parsing (Public Playlists) ===
        print(f"🎯 Method 1: Attempting ytInitialData JSON parsing...")
        initial_data_patterns = [
            r'var ytInitialData = ({.*?});',
            r'window\["ytInitialData"\] = ({.*?});',
            r'ytInitialData":\s*({.*?})(?:,|\})',
        ]

        for pattern in initial_data_patterns:
            initial_data_match = re.search(pattern, response.text, re.DOTALL)
            if initial_data_match:
                try:
                    data = json.loads(initial_data_match.group(1))

                    # Navigate through the complex YouTube data structure
                    contents = data.get('contents', {})
                    two_column = contents.get('twoColumnBrowseResultsRenderer', {})
                    tabs = two_column.get('tabs', [])

                    for tab in tabs:
                        tab_renderer = tab.get('tabRenderer', {})
                        content = tab_renderer.get('content', {})
                        section_list = content.get('sectionListRenderer', {})
                        contents_list = section_list.get('contents', [])

                        for content_item in contents_list:
                            item_section = content_item.get('itemSectionRenderer', {})
                            contents_inner = item_section.get('contents', [])

                            for inner_content in contents_inner:
                                playlist_video_list = inner_content.get('playlistVideoListRenderer', {})
                                contents_videos = playlist_video_list.get('contents', [])

                                for video_item in contents_videos:
                                    video_renderer = video_item.get('playlistVideoRenderer', {})
                                    video_id = video_renderer.get('videoId')

                                    if video_id and video_id not in video_ids:
                                        video_ids.append(video_id)
                                        print(f"  ✓ Found video: {video_id}")

                    if video_ids:
                        print(f"✅ Method 1 (ytInitialData): Successfully found {len(video_ids)} videos")
                        break

                except (json.JSONDecodeError, KeyError) as e:
                    print(f"  ⚠ Error parsing ytInitialData pattern: {e}")
                    continue

        # === METHOD 2: FALLBACK 1 - playlistVideoListRenderer Search (Unlisted Playlists) ===
        if not video_ids:
            print(f"🎯 Method 2: Searching for playlistVideoListRenderer (unlisted playlist support)...")

            # Look for any script tag containing playlistVideoListRenderer
            # This is more robust for unlisted playlists with different page structures
            playlist_renderer_pattern = r'"playlistVideoListRenderer":\s*\{[^}]*"contents":\s*\[([^\]]*)\]'
            renderer_match = re.search(playlist_renderer_pattern, response.text, re.DOTALL)

            if renderer_match:
                try:
                    # Extract the contents array and parse it
                    contents_text = renderer_match.group(1)

                    # Find all video IDs within the contents
                    video_id_pattern = r'"videoId":\s*"([a-zA-Z0-9_-]{11})"'
                    matches = re.findall(video_id_pattern, contents_text)

                    # Remove duplicates while preserving order
                    seen = set()
                    for video_id in matches:
                        if video_id not in seen:
                            video_ids.append(video_id)
                            seen.add(video_id)
                            print(f"  ✓ Found video: {video_id}")

                    if video_ids:
                        print(f"✅ Method 2 (playlistVideoListRenderer): Successfully found {len(video_ids)} videos")

                except Exception as e:
                    print(f"  ⚠ Error parsing playlistVideoListRenderer: {e}")

        # === METHOD 3: FALLBACK 2 - Enhanced Regex Patterns (Edge Cases) ===
        if not video_ids:
            print(f"🎯 Method 3: Trying enhanced regex fallback patterns...")

            # Multiple patterns to catch different JSON structures
            enhanced_patterns = [
                r'"videoId":"([a-zA-Z0-9_-]{11})"',  # Standard JSON format
                r'/watch\?v=([a-zA-Z0-9_-]{11})',    # URL format
                r'watch\?v=([a-zA-Z0-9_-]{11})',     # Partial URL format
                r'"([a-zA-Z0-9_-]{11})"[^"]*"videoId"',  # Reverse pattern
                r'videoId["\s]*:["\s]*([a-zA-Z0-9_-]{11})'  # Flexible JSON
            ]

            for i, pattern in enumerate(enhanced_patterns):
                matches = re.findall(pattern, response.text)
                if matches:
                    # Remove duplicates while preserving order
                    seen = set()
                    for video_id in matches:
                        if video_id not in seen and len(video_id) == 11:
                            video_ids.append(video_id)
                            seen.add(video_id)
                            print(f"  ✓ Found video: {video_id}")

                    if video_ids:
                        print(f"✅ Method 3 (pattern {i+1}): Successfully found {len(video_ids)} videos")
                        break

        # === FINAL RESULT EVALUATION ===
        if video_ids:
            print(f"🎉 SUCCESS: Extracted {len(video_ids)} video IDs from playlist")
            print(f"   First few videos: {video_ids[:3]}{'...' if len(video_ids) > 3 else ''}")
            return video_ids
        else:
            # === GRACEFUL FAILURE WITH DETAILED DIAGNOSTICS ===
            print(f"❌ FAILED: No video IDs found in playlist")
            print(f"   Playlist ID: {playlist_id}")
            print(f"   URL: {playlist_url}")

            # Check if playlist might be private or deleted
            if "This playlist is private" in response.text:
                print(f"   → Reason: Playlist is private")
            elif "Video unavailable" in response.text:
                print(f"   → Reason: Playlist may be deleted or unavailable")
            elif "playlist does not exist" in response.text.lower():
                print(f"   → Reason: Playlist does not exist")
            else:
                print(f"   → Reason: Unknown - playlist may be unlisted with restricted access")
                print(f"   → Suggestion: Verify the playlist URL is correct and accessible")

            return []

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error scraping playlist: {e}")
        return []
    except Exception as e:
        print(f"❌ Error scraping playlist: {e}")
        return []


def scrape_channel_data(channel_identifier, channel_name=None):
    """
    ENHANCED ROBUST CHANNEL SCRAPER with ID-First Strategy

    Scrapes channel data from YouTube channel about page using canonical channel IDs
    when available, with intelligent fallback for name-based discovery.

    Strategy:
    1. Primary: Use canonical channel ID (UC...) for direct, reliable access
    2. Fallback 1: Name-based URL patterns (legacy support)
    3. Fallback 2: YouTube search to discover channel ID from name
    4. Final: Graceful degradation with minimal data

    Args:
        channel_identifier (str): Channel ID (UC...) or channel name
        channel_name (str, optional): Channel display name (used when identifier is an ID)

    Returns:
        dict: Channel data with subscriber count, logo, etc. or None if failed
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }

    # Determine the actual channel name for display purposes
    display_name = channel_name or channel_identifier

    # STRATEGY 1: If we have a canonical channel ID, use it directly (most reliable)
    if channel_identifier.startswith('UC') and len(channel_identifier) == 24:
        print(f"Using canonical channel ID: {channel_identifier}")
        possible_urls = [f"https://www.youtube.com/channel/{channel_identifier}/about"]
    else:
        # STRATEGY 2: Try to discover the canonical channel ID through search
        print(f"Attempting to discover channel ID for: {channel_identifier}")
        discovered_id = _discover_channel_id_from_search(channel_identifier)

        if discovered_id:
            print(f"✓ Discovered canonical channel ID: {discovered_id}")
            possible_urls = [f"https://www.youtube.com/channel/{discovered_id}/about"]
        else:
            # STRATEGY 3: Generate possible URLs for name-based lookup (legacy fallback)
            print(f"Using name-based URL generation for: {channel_identifier}")
            possible_urls = [
                f"https://www.youtube.com/@{channel_identifier}/about",
                f"https://www.youtube.com/c/{channel_identifier}/about",
                f"https://www.youtube.com/user/{channel_identifier}/about",
                f"https://www.youtube.com/channel/{channel_identifier}/about"
            ]

    for url in possible_urls:
        try:
            print(f"Trying to scrape channel data from: {url}")
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()

            # Initialize default values
            subscriber_count = "Unknown"
            subscriber_count_raw = 0
            channel_logo_url = ""
            channel_id = ""
            total_views = "Unknown"  # NEW: Total channel views
            total_views_raw = 0      # NEW: Raw total views for sorting
            total_videos = 0         # NEW: Total video count

            # Get the full HTML text for comprehensive searching
            html_text = response.text
            soup = BeautifulSoup(html_text, 'html.parser')

            # === TIER 1: PRIMARY - ytInitialData JSON Parsing (Most Reliable) ===
            print(f"  Tier 1: Attempting ytInitialData JSON parsing...")
            subscriber_count, subscriber_count_raw, channel_logo_url, channel_id, total_views, total_views_raw, total_videos = _extract_from_initial_data(html_text)

            # === TIER 2: FALLBACK 1 - Enhanced Regex Patterns on Full HTML ===
            if subscriber_count == "Unknown":
                print(f"  Tier 2: Attempting enhanced regex pattern matching...")
                subscriber_count, subscriber_count_raw = _extract_subscriber_with_regex(html_text)

            if not channel_logo_url:
                print(f"  Tier 2: Attempting logo extraction with regex...")
                channel_logo_url = _extract_logo_with_regex(html_text)

            # === TIER 3: FALLBACK 2 - Alternative JSON Structures and Meta Tags ===
            if subscriber_count == "Unknown" or not channel_logo_url:
                print(f"  Tier 3: Attempting alternative extraction methods...")
                if subscriber_count == "Unknown":
                    subscriber_count, subscriber_count_raw = _extract_subscriber_alternative(html_text, soup)
                if not channel_logo_url:
                    channel_logo_url = _extract_logo_alternative(html_text, soup)

            # === SUCCESS CHECK ===
            if subscriber_count != "Unknown" or channel_logo_url:
                print(f"  ✓ Successfully extracted channel data!")
                print(f"    Subscribers: {subscriber_count}")
                print(f"    Logo: {'Found' if channel_logo_url else 'Not found'}")

                now = datetime.now(timezone.utc)
                # Use permanent channel ID for URL if available, otherwise fallback to current URL
                final_channel_id = channel_id or channel_name
                if channel_id and channel_id.startswith('UC'):
                    # Use the permanent channel ID URL format
                    channel_url = f'https://www.youtube.com/channel/{channel_id}'
                else:
                    # Fallback to the working URL we found
                    channel_url = url.replace('/about', '')

                return {
                    'channelId': final_channel_id,
                    'name': display_name,
                    'logoUrl': channel_logo_url or 'https://www.youtube.com/img/desktop/yt_1200.png',
                    'subscriberCount': subscriber_count,
                    'subscriberCountRaw': subscriber_count_raw,
                    'totalViews': total_views,        # NEW: Total channel views
                    'totalViewsRaw': total_views_raw, # NEW: Raw total views for sorting
                    'totalVideos': total_videos,      # NEW: Total video count
                    'lastUpdated': now.isoformat(),
                    'url': channel_url
                }
            else:
                print(f"  ✗ No data extracted from this URL, trying next...")

        except Exception as e:
            print(f"  ✗ Error scraping channel from {url}: {e}")
            continue

    # === FINAL FALLBACK: Graceful Degradation ===
    print(f"✗ All extraction methods failed for: {display_name}")
    now = datetime.now(timezone.utc)
    return {
        'channelId': channel_identifier,
        'name': display_name,
        'logoUrl': 'https://www.youtube.com/img/desktop/yt_1200.png',
        'subscriberCount': 'Unknown',
        'subscriberCountRaw': 0,
        'totalViews': 'Unknown',     # NEW: Default total views
        'totalViewsRaw': 0,          # NEW: Default raw total views
        'totalVideos': 0,            # NEW: Default total videos
        'lastUpdated': now.isoformat(),
        'url': f'https://www.youtube.com/@{display_name}'
    }


def _extract_from_initial_data(html_text):
    """
    TIER 1: Extract channel data from ytInitialData JSON (Primary Method)
    This is the most reliable method as it uses structured data.
    """
    subscriber_count = "Unknown"
    subscriber_count_raw = 0
    channel_logo_url = ""
    channel_id = ""
    total_views = "Unknown"  # NEW: Total channel views
    total_views_raw = 0      # NEW: Raw total views
    total_videos = 0         # NEW: Total video count

    try:
        # Look for ytInitialData with more flexible regex
        initial_data_patterns = [
            r'var ytInitialData = ({.*?});',
            r'window\["ytInitialData"\] = ({.*?});',
            r'ytInitialData":\s*({.*?})(?:,|\})',
        ]

        initial_data = None
        for pattern in initial_data_patterns:
            match = re.search(pattern, html_text, re.DOTALL)
            if match:
                try:
                    initial_data = json.loads(match.group(1))
                    break
                except json.JSONDecodeError:
                    continue

        if not initial_data:
            return subscriber_count, subscriber_count_raw, channel_logo_url, channel_id, total_views, total_views_raw, total_videos

        # Navigate through multiple possible header structures
        header_paths = [
            ['header', 'c4TabbedHeaderRenderer'],
            ['header', 'pageHeaderRenderer', 'content', 'pageHeaderViewModel'],
            ['contents', 'twoColumnBrowseResultsRenderer', 'tabs', 0, 'tabRenderer', 'content', 'sectionListRenderer', 'contents', 0, 'itemSectionRenderer', 'contents', 0, 'channelAboutFullMetadataRenderer']
        ]

        renderer = None
        for path in header_paths:
            temp = initial_data
            try:
                for key in path:
                    if isinstance(key, int):
                        temp = temp[key]
                    else:
                        temp = temp.get(key, {})
                if temp:
                    renderer = temp
                    break
            except (KeyError, IndexError, TypeError):
                continue

        if renderer:
            # Extract subscriber count with multiple fallback paths
            sub_paths = [
                ['subscriberCountText', 'simpleText'],
                ['subscriberCountText', 'runs', 0, 'text'],
                ['subtitle', 'runs', 0, 'text'],
                ['metadata', 'channelMetadataRenderer', 'subscriberCountText', 'simpleText']
            ]

            for path in sub_paths:
                try:
                    temp = renderer
                    for key in path:
                        if isinstance(key, int):
                            temp = temp[key]
                        else:
                            temp = temp.get(key, {})

                    if temp and isinstance(temp, str) and ('subscriber' in temp.lower() or temp.replace(',', '').replace('.', '').replace('K', '').replace('M', '').replace('B', '').isdigit()):
                        subscriber_count = format_subscriber_count(temp)
                        subscriber_count_raw = _parse_subscriber_count_to_raw(temp)
                        break
                except (KeyError, IndexError, TypeError):
                    continue

            # Extract channel logo with multiple fallback paths
            logo_paths = [
                ['avatar', 'thumbnails'],
                ['image', 'thumbnails'],
                ['thumbnail', 'thumbnails'],
                ['channelThumbnailWithLinkRenderer', 'thumbnail', 'thumbnails']
            ]

            for path in logo_paths:
                try:
                    temp = renderer
                    for key in path:
                        temp = temp.get(key, {})

                    if isinstance(temp, list) and temp:
                        # Get the highest quality thumbnail (usually the last one)
                        channel_logo_url = temp[-1].get('url', '')
                        if channel_logo_url:
                            break
                except (KeyError, TypeError):
                    continue

        # Extract channel ID from metadata
        try:
            metadata = initial_data.get('metadata', {})
            if 'channelMetadataRenderer' in metadata:
                channel_id = metadata['channelMetadataRenderer'].get('externalId', '')
        except (KeyError, TypeError):
            pass

        # ENHANCED: Extract total views and video count from about page data
        try:
            # Look for channel statistics in the about page data
            contents = initial_data.get('contents', {})
            two_column = contents.get('twoColumnBrowseResultsRenderer', {})
            tabs = two_column.get('tabs', [])

            for tab in tabs:
                tab_renderer = tab.get('tabRenderer', {})
                content = tab_renderer.get('content', {})
                section_list = content.get('sectionListRenderer', {})
                contents_list = section_list.get('contents', [])

                for content_item in contents_list:
                    item_section = content_item.get('itemSectionRenderer', {})
                    contents_inner = item_section.get('contents', [])

                    for inner_content in contents_inner:
                        # Look for channel about metadata
                        about_renderer = inner_content.get('channelAboutFullMetadataRenderer', {})
                        if about_renderer:
                            # Extract view count
                            view_count_text = about_renderer.get('viewCountText', {})
                            if isinstance(view_count_text, dict):
                                view_text = view_count_text.get('simpleText', '')
                                if view_text and 'views' in view_text.lower():
                                    total_views = view_text
                                    # Extract raw number for sorting
                                    import re
                                    numbers = re.findall(r'[\d,]+', view_text.replace(',', ''))
                                    if numbers:
                                        total_views_raw = int(numbers[0])

                            # Extract video count from description or stats
                            description = about_renderer.get('description', {})
                            if isinstance(description, dict):
                                desc_text = description.get('simpleText', '')
                                # Look for video count patterns in description
                                video_patterns = [
                                    r'(\d+)\s+videos?',
                                    r'(\d+)\s+uploads?'
                                ]
                                for pattern in video_patterns:
                                    match = re.search(pattern, desc_text, re.IGNORECASE)
                                    if match:
                                        total_videos = int(match.group(1))
                                        break

                            break

                    if total_views != "Unknown":
                        break

                if total_views != "Unknown":
                    break

        except (KeyError, TypeError, ValueError) as e:
            print(f"    Could not extract enhanced stats: {e}")

    except Exception as e:
        print(f"    Error in Tier 1 extraction: {e}")

    return subscriber_count, subscriber_count_raw, channel_logo_url, channel_id, total_views, total_views_raw, total_videos


def _extract_subscriber_with_regex(html_text):
    """
    TIER 2: Extract subscriber count using enhanced regex patterns
    """
    subscriber_count = "Unknown"
    subscriber_count_raw = 0

    # Comprehensive regex patterns for subscriber count
    patterns = [
        # JSON-like patterns
        r'"subscriberCountText":\s*\{\s*"simpleText":\s*"([^"]+)"',
        r'"subscriberCountText":\s*\{\s*"runs":\s*\[\s*\{\s*"text":\s*"([^"]+)"',
        r'"subtitle":\s*\{\s*"runs":\s*\[\s*\{\s*"text":\s*"([^"]+)"',

        # Direct text patterns
        r'(\d+(?:\.\d+)?[KMB]?)\s+subscribers?',
        r'(\d{1,3}(?:,\d{3})*)\s+subscribers?',
        r'subscribers?["\s]*:\s*["\s]*(\d+(?:\.\d+)?[KMB]?)',

        # Aria-label patterns
        r'aria-label="[^"]*(\d+(?:\.\d+)?[KMB]?)\s+subscribers?[^"]*"',
        r'title="[^"]*(\d+(?:\.\d+)?[KMB]?)\s+subscribers?[^"]*"',

        # Meta tag patterns
        r'<meta[^>]*content="[^"]*(\d+(?:\.\d+)?[KMB]?)\s+subscribers?[^"]*"',
    ]

    for pattern in patterns:
        matches = re.findall(pattern, html_text, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                # Clean and validate the match
                clean_match = match.strip()
                if clean_match and (clean_match.replace(',', '').replace('.', '').replace('K', '').replace('M', '').replace('B', '').isdigit() or
                                   any(suffix in clean_match.upper() for suffix in ['K', 'M', 'B'])):
                    subscriber_count = format_subscriber_count(clean_match)
                    subscriber_count_raw = _parse_subscriber_count_to_raw(clean_match)
                    return subscriber_count, subscriber_count_raw

    return subscriber_count, subscriber_count_raw


def _extract_logo_with_regex(html_text):
    """
    TIER 2: Extract channel logo using enhanced regex patterns
    """
    # Comprehensive regex patterns for channel logo
    patterns = [
        # Avatar/thumbnail patterns in JSON
        r'"avatar":\s*\{\s*"thumbnails":\s*\[\s*[^]]*\{\s*"url":\s*"([^"]+)"',
        r'"channelThumbnailWithLinkRenderer":\s*\{[^}]*"thumbnail":\s*\{[^}]*"thumbnails":\s*\[[^]]*"url":\s*"([^"]+)"',
        r'"image":\s*\{\s*"thumbnails":\s*\[[^]]*"url":\s*"([^"]+)"',

        # Direct image patterns
        r'<img[^>]*class="[^"]*avatar[^"]*"[^>]*src="([^"]+)"',
        r'<img[^>]*src="([^"]*yt3\.ggpht\.com[^"]*)"',
        r'<link[^>]*rel="image_src"[^>]*href="([^"]+)"',

        # Meta tag patterns
        r'<meta[^>]*property="og:image"[^>]*content="([^"]+)"',
        r'<meta[^>]*name="twitter:image"[^>]*content="([^"]+)"',
    ]

    for pattern in patterns:
        matches = re.findall(pattern, html_text, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                # Validate that it looks like a valid image URL
                if match and ('yt3.ggpht.com' in match or 'ytimg.com' in match or match.startswith('http')):
                    return match

    return ""


def _extract_subscriber_alternative(html_text, soup):
    """
    TIER 3: Alternative methods for subscriber extraction
    """
    subscriber_count = "Unknown"
    subscriber_count_raw = 0

    try:
        # Method 1: Look for specific meta tags
        meta_tags = soup.find_all('meta')
        for tag in meta_tags:
            content = tag.get('content', '')
            if 'subscriber' in content.lower():
                # Extract number from content
                match = re.search(r'(\d+(?:\.\d+)?[KMB]?)', content)
                if match:
                    subscriber_count = format_subscriber_count(match.group(1))
                    subscriber_count_raw = _parse_subscriber_count_to_raw(match.group(1))
                    return subscriber_count, subscriber_count_raw

        # Method 2: Look in script tags for alternative JSON structures
        script_tags = soup.find_all('script')
        for script in script_tags:
            if script.string:
                # Look for alternative data structures
                alt_patterns = [
                    r'"subscriberCount":\s*"?(\d+(?:\.\d+)?[KMB]?)"?',
                    r'"subscribers":\s*"?(\d+(?:\.\d+)?[KMB]?)"?',
                    r'"followerCount":\s*"?(\d+(?:\.\d+)?[KMB]?)"?',
                ]

                for pattern in alt_patterns:
                    matches = re.findall(pattern, script.string, re.IGNORECASE)
                    if matches:
                        subscriber_count = format_subscriber_count(matches[0])
                        subscriber_count_raw = _parse_subscriber_count_to_raw(matches[0])
                        return subscriber_count, subscriber_count_raw

    except Exception as e:
        print(f"    Error in alternative subscriber extraction: {e}")

    return subscriber_count, subscriber_count_raw


def _extract_logo_alternative(html_text, soup):
    """
    TIER 3: Alternative methods for logo extraction
    """
    try:
        # Method 1: Look for link tags with icon relations
        link_tags = soup.find_all('link', rel=['icon', 'shortcut icon', 'apple-touch-icon'])
        for tag in link_tags:
            href = tag.get('href', '')
            if href and ('yt3.ggpht.com' in href or 'ytimg.com' in href):
                return href

        # Method 2: Look for any img tags with channel-related classes or IDs
        img_tags = soup.find_all('img')
        for img in img_tags:
            src = img.get('src', '')
            alt = img.get('alt', '').lower()
            class_name = ' '.join(img.get('class', [])).lower()

            if src and ('yt3.ggpht.com' in src or 'ytimg.com' in src):
                if any(keyword in alt or keyword in class_name for keyword in ['channel', 'avatar', 'profile', 'logo']):
                    return src

        # Method 3: Look in structured data (JSON-LD)
        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        for script in json_ld_scripts:
            if script.string:
                try:
                    data = json.loads(script.string)
                    if isinstance(data, dict):
                        # Look for image in various structured data formats
                        image_fields = ['image', 'logo', 'thumbnail', 'avatar']
                        for field in image_fields:
                            if field in data:
                                image_data = data[field]
                                if isinstance(image_data, str):
                                    return image_data
                                elif isinstance(image_data, dict) and 'url' in image_data:
                                    return image_data['url']
                                elif isinstance(image_data, list) and image_data:
                                    if isinstance(image_data[0], str):
                                        return image_data[0]
                                    elif isinstance(image_data[0], dict) and 'url' in image_data[0]:
                                        return image_data[0]['url']
                except json.JSONDecodeError:
                    continue

    except Exception as e:
        print(f"    Error in alternative logo extraction: {e}")

    return ""


def _parse_subscriber_count_to_raw(sub_text):
    """
    Helper function to convert subscriber text to raw number for sorting
    """
    if not sub_text:
        return 0

    try:
        # Clean the string
        clean_str = sub_text.lower().replace('subscribers', '').replace('subscriber', '').strip()

        # Handle different formats
        if 'k' in clean_str:
            return int(float(clean_str.replace('k', '')) * 1000)
        elif 'm' in clean_str:
            return int(float(clean_str.replace('m', '')) * 1000000)
        elif 'b' in clean_str:
            return int(float(clean_str.replace('b', '')) * 1000000000)
        else:
            # Handle comma-separated numbers
            return int(clean_str.replace(',', ''))
    except (ValueError, AttributeError):
        return 0


def background_scrape_and_save(video_id):
    """
    BACKGROUND WORKER FUNCTION for asynchronous video scraping.

    This function is designed to run in a separate thread and safely update
    the video cache with scraped data. It finds the placeholder object for
    the given video_id and replaces it with full scraped data.

    Args:
        video_id (str): The YouTube video ID to scrape and update

    Returns:
        bool: True if successful, False if failed
    """
    import json
    import os
    import traceback
    from datetime import datetime, timezone

    try:
        print(f"🔄 Background worker started for video: {video_id}")

        # STEP 1: Scrape the video data
        try:
            video_data = scrape_youtube_video_to_object(video_id)
        except Exception as scrape_error:
            print(f"--- SCRAPING ERROR for video {video_id} ---")
            print(f"Error during scrape_youtube_video_to_object: {scrape_error}")
            traceback.print_exc()
            _update_placeholder_with_error(video_id, f"Scraping failed: {scrape_error}")
            return False

        if not video_data:
            print(f"❌ Failed to scrape video data for: {video_id} (returned None/empty)")
            _update_placeholder_with_error(video_id, "No video data returned from scraper")
            return False

        # STEP 2: Load current video cache (thread-safe file reading)
        try:
            videos = _load_video_cache_safe()
        except Exception as load_error:
            print(f"--- CACHE LOADING ERROR for video {video_id} ---")
            print(f"Error loading video cache: {load_error}")
            traceback.print_exc()
            _update_placeholder_with_error(video_id, f"Cache loading failed: {load_error}")
            return False

        # STEP 3: Find and update the placeholder object
        updated = False
        try:
            for i, video in enumerate(videos):
                if video.get('id') == video_id and video.get('status') == 'pending':
                    # Replace placeholder with full scraped data
                    # Preserve the original dateAdded from placeholder
                    video_data['dateAdded'] = video.get('dateAdded', video_data.get('dateAdded'))
                    video_data['status'] = 'completed'  # Mark as completed
                    videos[i] = video_data
                    updated = True
                    print(f"✅ Updated placeholder for video: {video_data.get('title', video_id)}")
                    break
        except Exception as update_error:
            print(f"--- PLACEHOLDER UPDATE ERROR for video {video_id} ---")
            print(f"Error updating placeholder: {update_error}")
            traceback.print_exc()
            _update_placeholder_with_error(video_id, f"Placeholder update failed: {update_error}")
            return False

        if not updated:
            print(f"⚠️ No placeholder found for video: {video_id}")
            _update_placeholder_with_error(video_id, "No pending placeholder found in cache")
            return False

        # STEP 4: Save updated cache (thread-safe file writing)
        try:
            _save_video_cache_safe(videos)
        except Exception as save_error:
            print(f"--- CACHE SAVING ERROR for video {video_id} ---")
            print(f"Error saving video cache: {save_error}")
            traceback.print_exc()
            _update_placeholder_with_error(video_id, f"Cache saving failed: {save_error}")
            return False

        print(f"🎉 Background scraping completed for: {video_data.get('title', video_id)}")
        return True

    except Exception as e:
        print(f"--- FATAL ERROR IN BACKGROUND SCRAPER for video {video_id} ---")
        print(f"Unexpected error: {e}")
        traceback.print_exc()
        _update_placeholder_with_error(video_id, f"Fatal error: {e}")
        return False


def _load_video_cache_safe():
    """Thread-safe video cache loading with file locking."""
    import json
    import os

    cache_file = 'videos.json'

    if not os.path.exists(cache_file):
        return []

    try:
        with open(cache_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return []


def _save_video_cache_safe(videos):
    """Thread-safe video cache saving with atomic write."""
    import json
    import tempfile
    import os

    cache_file = 'videos.json'

    # Use atomic write: write to temp file, then rename
    with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False,
                                     dir=os.path.dirname(cache_file) or '.') as temp_file:
        json.dump(videos, temp_file, indent=2, ensure_ascii=False)
        temp_file_path = temp_file.name

    # Atomic rename (works on most filesystems)
    try:
        if os.name == 'nt':  # Windows
            if os.path.exists(cache_file):
                os.remove(cache_file)
        os.rename(temp_file_path, cache_file)
    except Exception as e:
        # Cleanup temp file if rename failed
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        raise e


def _update_placeholder_with_error(video_id, error_message="Failed to scrape video data"):
    """Update placeholder object with error status and detailed error message."""
    from datetime import datetime, timezone
    import traceback

    try:
        videos = _load_video_cache_safe()

        for i, video in enumerate(videos):
            if video.get('id') == video_id and video.get('status') == 'pending':
                videos[i]['status'] = 'error'
                videos[i]['title'] = f'Error scraping: {video_id}'
                videos[i]['error_message'] = error_message
                videos[i]['lastUpdated'] = datetime.now(timezone.utc).isoformat()
                break

        _save_video_cache_safe(videos)
        print(f"❌ Marked video as error: {video_id} - {error_message}")

    except Exception as e:
        print(f"💥 Failed to update error status for {video_id}: {e}")
        traceback.print_exc()


def _discover_channel_id_from_search(channel_name):
    """
    Intelligent Channel ID Discovery through YouTube Search

    When we only have a channel name, this function searches YouTube to find
    the canonical channel ID (UC...) for more reliable scraping.

    Args:
        channel_name (str): The channel name to search for

    Returns:
        str: The canonical channel ID (UC...) if found, None otherwise
    """
    try:
        import urllib.parse

        # URL encode the channel name for search
        encoded_name = urllib.parse.quote_plus(channel_name)

        # Construct YouTube search URL with channel filter
        # The sp parameter filters results to show only channels
        search_url = f"https://www.youtube.com/results?search_query={encoded_name}&sp=EgIQAg%253D%253D"

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
        }

        print(f"  🔍 Searching YouTube for channel: {channel_name}")
        response = requests.get(search_url, headers=headers, timeout=10)
        response.raise_for_status()

        # Look for channel links in the search results
        # Pattern to match channel URLs with UC IDs
        channel_id_patterns = [
            r'/channel/(UC[a-zA-Z0-9_-]{22})',  # Standard channel ID format
            r'"channelId":"(UC[a-zA-Z0-9_-]{22})"',  # JSON format
            r'"browseEndpoint":\s*\{\s*"browseId":\s*"(UC[a-zA-Z0-9_-]{22})"',  # Browse endpoint format
        ]

        for pattern in channel_id_patterns:
            matches = re.findall(pattern, response.text)
            if matches:
                # Return the first match (most relevant result)
                channel_id = matches[0]
                print(f"  ✓ Found channel ID: {channel_id}")
                return channel_id

        print(f"  ✗ No channel ID found in search results for: {channel_name}")
        return None

    except Exception as e:
        print(f"  ✗ Error during channel ID discovery: {e}")
        return None
